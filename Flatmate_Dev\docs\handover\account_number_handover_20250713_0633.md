# Account Number Extraction Handover

## Date: 2025-07-13 06:33:00

## Current State
- The account number extraction in `_base_statement_handler.py` was being refactored
- Changes have been reverted to the last known good version
- Core issue was with how account numbers are validated in `can_handle_file`
 >> and the information recieved from _extract account number .. many false positives.

## Key Findings

### Account Number Extraction Logic
1. **Extraction Order**:
   - Metadata (if `in_metadata=True`)
   - Data column (if `in_data=True` and `has_account_column=True`)
   - Filename (if `in_file_name=True` and filepath provided)

2. **Co-op Bank Specifics**:
   - Uses filename for account number extraction
   - Pattern: `02-\d{4}-\d{7}-\d{3}`
   - No account column in data (`has_account_column=False`)

### Issues Identified
1. **Validation Logic**:
   - `can_handle_file` wasn't properly validating account number location
   - Some handlers were getting false positives on account number matches

2. **Pattern Matching**:
   - Inconsistent use of `re.fullmatch` vs `re.search`
   - Some handlers needed exact matches while others needed partial matches

## Recommendations

1. **For Co-op Bank Handler**:
   - Keep `in_file_name=True`
   - Ensure `has_account_column=False`
   - No location needed for filename matching

2. **For Other Handlers**:
   - Use `in_metadata` or `in_data` as appropriate
   - Set `has_account_column` correctly
   - Provide proper location if using metadata/data column

## Next Steps
1. Review the current implementation in `_base_statement_handler_LAST_GOOD_VERSION.py`
2. Apply minimal changes to fix the account number validation
3. Test with real statement files for all bank types
