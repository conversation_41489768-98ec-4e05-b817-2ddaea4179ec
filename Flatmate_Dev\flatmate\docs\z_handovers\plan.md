2025-07-11 @ 16:55:14

# Flatmate Column Management Refactor Plan

## Notes
- The Columns registry is the central source of truth for all column definitions and their groupings.
- The duplicate `modified_date` column error was caused by the Transaction dataclass including all columns, including system-managed ones, due to indiscriminate use of `get_all_columns()`.
- The correct fix is to use a helper method (e.g., `get_transaction_columns`) that excludes 'db_system' columns, not to remove them from the registry.
- The goal is to minimize the need for helper files like `column_manager.py` and `column_name_service.py` by consolidating logic into the Columns class.
- The database repository should be the only place that directly manages or references system columns.
- The Columns class should be the single point of truth for all column-related logic, including group-based access and any dynamic subsets needed by the application (such as for the Transaction dataclass).
- Helper functions for column selection (like `get_transaction_columns`) should live in the Columns class to prevent fragmentation and confusion.
- Once the current fix is validated, refactor or remove redundant helpers/services to simplify the codebase and improve maintainability.
- Document the intended use and boundaries of each group (e.g., 'db_system', 'core_transaction', 'user_editable') to guide future development and avoid similar issues.
- Recent fixes included: cleaning up the test database before runs, aligning ImportResult fields, and ensuring correct propagation of database update results to the test script.
- Column width and default visibility logic have been consolidated into the Columns class, and legacy usages in transaction_view_panel.py and table_config_v2.py have been refactored.
- Legacy `ColumnNameService` and `ColumnManager` have been fully removed from the codebase and exports.
- The speculative `converters.py` (CSVToTransactionConverter, TransactionToCSVConverter) has been archived after confirming it was unused in active code.
- The speculative `events.py` in helpers was confirmed unused and has been deleted; the real event bus is in core/services/event_bus.py.
- Documentation is being created in `columns_readme.md` to clarify naming conventions, group boundaries, and usage patterns for the new column system.

## Task List
- [x] Diagnose root cause of duplicate column error
- [x] Add `get_transaction_columns` method to Columns registry
- [x] Update `_create_transaction_class` to use `get_transaction_columns`
- [x] Verify fix by running the integration test (`test_dw_pipeline.py`)
- [x] Refactor transaction_view_panel.py to use Columns class
- [x] Refactor table_config_v2.py to use Columns class
- [x] Search for and remove remaining usages of ColumnNameService and ColumnManager
- [x] Remove public export of ColumnNameService from standards/__init__.py
- [x] Review and plan deprecation/refactor of `column_manager.py` and `column_name_service.py`
- [ ] Clarify and document column naming conventions (`db_name` vs `display_name`)
- [x] Refactor or remove redundant helpers/services (converters archived, helpers/events deleted, core/services/event_bus retained)
- [ ] Document the intended use and boundaries of each group

## Current Goal
Document column conventions and finalize codebase cleanup

---
Documentation for columns is being written in `columns_readme.md`.