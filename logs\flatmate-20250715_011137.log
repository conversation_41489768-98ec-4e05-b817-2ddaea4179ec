2025-07-15 01:11:37 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 01:11:38 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 01:11:38 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 01:11:38 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 01:11:38 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 01:11:38 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 01:11:38 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 01:11:38 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 01:11:38 - [fm.main] [INFO] - Application starting...
2025-07-15 01:11:39 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 01:11:39 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 01:11:39 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'update_data', 'categorize']
2025-07-15 01:11:39 - [fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 01:11:39 - [fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 01:11:39 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-15 01:11:39 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 01:11:40 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 01:11:40 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 01:11:40 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 01:11:41 - [fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 01:11:41 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-15 01:11:48 - [fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-15 01:11:48 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-15 01:11:48 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 01:11:48 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-15 01:11:48 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 01:11:48 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 01:11:48 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from DB for categorisation…
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {}
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions from database
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [DEBUG] - Converted to DataFrame with shape: (2099, 30)
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-15 01:12:04 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-15 01:12:04 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-15 01:12:04 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 01:12:04 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-15 01:12:06 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Account', 'Date', 'Details', 'Amount', 'Category', 'Tags', 'Notes']
2025-07-15 01:12:06 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed transactions
