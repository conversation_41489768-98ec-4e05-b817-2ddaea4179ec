# Statement Handlers Refactoring - Handover Document

## Project Overview
This project involves refactoring the bank statement handler system for the Flatmate application to implement consistent error handling and logging across all statement handlers. The system processes various bank statement formats (CSV, PDF) and converts them into a standardized format for further processing.

## Key Files & Directories

### Core Components
- `flatmate/src/fm/modules/update_data/utils/statement_handlers/`
  - `_base_statement_handler.py` - Base class with common functionality
  - `_handler_registry.py` - Manages available statement handlers
  - `coop_standard_csv_handler.py` - Handles Co-op Bank statements
  - `kiwibank_basic_csv_handler.py` - Handles Kiwibank basic CSV format
  - `kiwibank_full_csv_handler.py` - Handles Kiwibank full transaction format
  - `asb_standard_csv_handler.py` - Handles ASB Bank statements

### Test Files
- `tests/test_real_csvs.py` - Main test script
- `tests/test_real_csvs_TEST_NOTES.md` - Detailed test notes and status
- `tests/test_real_csvs.run.bat` - Helper script to run tests
- `tests/test_CSVs/` - Sample statement files for testing

### Documentation
- `tests/test_real_csvs_TEST_NOTES.md` - Current test status and next steps
- `HANDOVER_statement_handlers.md` - This document

## Current Implementation Status

### What's Working
1. Base handler class with common functionality
2. Unified error handling pattern using `StatementError`
3. Centralized logging system
4. Test runner setup

### In Progress
1. Updating individual handlers to use new patterns
2. Fixing logger initialization issues
3. Standardizing error messages

### Pending
1. Complete handler updates for all bank formats
2. Comprehensive test coverage
3. Documentation updates

## Key Design Decisions

### Error Handling
- Using custom `StatementError` exception class
- `handle_errors` context manager for consistent error handling
- Structured error messages for better debugging

### Logging
- Centralized logger in base class
- Consistent log format across all handlers
- Debug information for troubleshooting

### Testing
- Real-world test files for each bank format
- Detailed test output with validation results
- Automated test runner with virtualenv support

## Getting Started

### Prerequisites
- Python 3.8+
- Virtual environment (`.venv_fm313`)
- Required packages (see `requirements.txt`)

### Running Tests
```bash
# Activate virtual environment
cd flatmate
.venv_fm313\Scripts\activate

# Run tests
python -m tests.test_real_csvs
# Or use the batch file
tests\test_real_csvs.run.bat
```

## Common Issues & Solutions

### Logger Not Initialized
**Symptom**: `'Handler' object has no attribute 'logger'`  
**Solution**: Ensure the handler's `__init__` method calls `super().__init__()`

### StatementError Not Defined
**Symptom**: `NameError: name 'StatementError' is not defined`  
**Solution**: Import `StatementError` from `_base_statement_handler`

### Test Failures
1. Check `statement_handler_test.log` for detailed error information
2. Verify input files match expected formats
3. Ensure all required columns are present in test files

## Next Steps

### High Priority
1. Fix logger initialization in all handlers
2. Update remaining handlers to use new error patterns
3. Add comprehensive test coverage

### Medium Priority
1. Refactor handler registration system
2. Add input validation
3. Improve error messages

### Low Priority
1. Performance optimization
2. Additional test cases
3. Documentation updates

## Contact Information
- Previous Developer: [Your Name]
- Last Updated: 2024-06-29
- Project: Flatmate Statement Handlers Refactoring
