2025-07-15 02:02:53 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 02:02:54 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 02:02:54 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 36 user preferences
2025-07-15 02:02:54 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.available_columns = ['date', 'details', 'amount', 'account', 'tags', 'category', 'notes'] (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_load_from_db = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.remember_last_query = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_sort_column = date (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_sort_order = descending (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.remember_visibility = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.remember_widths = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.remember_order = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.auto_size_enabled = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.max_column_width = 40 (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.details_expand_to_fill = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.enable_sorting = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.enable_filtering = True (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.selection_mode = extended (Source: test_real_config.py:test_real_categorize_config)
2025-07-15 02:02:55 - [fm.core.config.base_local_config_v2] [INFO] - Defaults YAML for categorize saved to: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
