#!/usr/bin/env python3
"""
Test the AccountSelector component.

This creates a standalone window to test the multi-select account selector
with long account numbers.
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QLabel, QHBoxLayout
from PySide6.QtCore import Qt

from fm.gui.styles import load_styles
from fm.gui._shared_components.widgets.account_selector import AccountSelector


class AccountSelectorTestWindow(QMainWindow):
    """Test window for the AccountSelector component."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Account Selector Test")
        self.setGeometry(100, 100, 700, 500)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Title
        title = QLabel("Account Selector Component Test")
        title.setStyleSheet("color: #CCCCCC; font-weight: bold; font-size: 16px;")
        main_layout.addWidget(title)
        
        # Test section
        test_layout = QHBoxLayout()
        
        # Left side - Account selector
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)
        
        selector_label = QLabel("Account Selector:")
        selector_label.setStyleSheet("color: #CCCCCC; font-weight: bold;")
        left_layout.addWidget(selector_label)
        
        # Create the account selector
        self.account_selector = AccountSelector()
        left_layout.addWidget(self.account_selector)
        
        # Add some test accounts with long numbers
        test_accounts = [
            "Checking-****************",
            "Savings-****************", 
            "Credit-****************",
            "Investment-****************",
            "Business-****************",
            "Personal-****************",
            "Joint-****************",
            "Emergency-****************"
        ]
        
        self.account_selector.set_available_accounts(test_accounts)
        
        left_layout.addStretch()
        left_widget.setMaximumWidth(300)
        test_layout.addWidget(left_widget)
        
        # Right side - Output area
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        output_label = QLabel("Selection Events:")
        output_label.setStyleSheet("color: #CCCCCC; font-weight: bold;")
        right_layout.addWidget(output_label)
        
        self.output_text = QTextEdit()
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        right_layout.addWidget(self.output_text)
        
        test_layout.addWidget(right_widget, 1)
        main_layout.addLayout(test_layout)
        
        # Connect signals
        self.account_selector.selection_changed.connect(self.on_selection_changed)
        
        # Initial output
        self.output_text.append("🧪 Account Selector Test Started")
        self.output_text.append("=" * 50)
        self.output_text.append("Features to test:")
        self.output_text.append("• Click account selector button")
        self.output_text.append("• Select/deselect individual accounts")
        self.output_text.append("• Use 'All Accounts' option")
        self.output_text.append("• Use 'Select All' and 'Select None' buttons")
        self.output_text.append("• Check button text updates")
        self.output_text.append("• Verify long account numbers are handled")
        self.output_text.append("")
        self.output_text.append(f"✅ Loaded {len(test_accounts)} test accounts")
        self.output_text.append("📋 Account list:")
        for i, account in enumerate(test_accounts, 1):
            self.output_text.append(f"  {i}. {account}")
        self.output_text.append("")
    
    def on_selection_changed(self, selected_accounts):
        """Handle account selection changes."""
        self.output_text.append(f"\n🔄 Account Selection Changed:")
        
        if not selected_accounts:
            self.output_text.append("  • Selection: All Accounts (no filter)")
        elif len(selected_accounts) == 1:
            account = next(iter(selected_accounts))
            self.output_text.append(f"  • Selection: Single account")
            self.output_text.append(f"    Account: {account}")
        else:
            self.output_text.append(f"  • Selection: {len(selected_accounts)} accounts")
            for i, account in enumerate(sorted(selected_accounts), 1):
                self.output_text.append(f"    {i}. {account}")
        
        # Show button text
        button_text = self.account_selector.selection_btn.text()
        self.output_text.append(f"  • Button text: '{button_text}'")
        
        self.scroll_to_bottom()
    
    def scroll_to_bottom(self):
        """Scroll output text to bottom."""
        scrollbar = self.output_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def test_account_selector():
    """Test the account selector component."""
    print("🧪 Testing Account Selector Component")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Load application styles
    try:
        styles = load_styles()
        app.setStyleSheet(styles)
        print("✅ Loaded application styles")
    except Exception as e:
        print(f"⚠️  Could not load styles: {e}")
        print("   Using default Qt styling")
    
    # Create and show test window
    window = AccountSelectorTestWindow()
    window.show()
    
    print("🎨 Test window created with AccountSelector")
    print("📋 Features to test:")
    print("  • Multi-select functionality")
    print("  • Long account number handling")
    print("  • Button text updates")
    print("  • Dialog selection interface")
    print("  • All/None selection options")
    
    # Run the application
    return app.exec()


if __name__ == "__main__":
    sys.exit(test_account_selector())
