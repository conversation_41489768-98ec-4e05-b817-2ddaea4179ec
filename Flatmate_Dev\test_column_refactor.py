#!/usr/bin/env python3
"""
Test script to verify the column system refactoring works correctly.
"""
import sys
from pathlib import Path

# Add the src directory to the path
src_dir = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_dir))

def test_new_columns_system():
    """Test the new Columns system."""
    print("Testing new Columns system...")
    
    try:
        from fm.core.data_services.standards.columns import Columns
        print("✓ Successfully imported Columns")
        
        # Test basic functionality
        all_columns = Columns.get_all_columns()
        print(f"✓ Found {len(all_columns)} columns")
        
        # Test specific columns
        print(f"✓ DATE column: db_name='{Columns.DATE.db_name}', display_name='{Columns.DATE.display_name}'")
        print(f"✓ DETAILS column: db_name='{Columns.DETAILS.db_name}', display_name='{Columns.DETAILS.display_name}'")
        print(f"✓ AMOUNT column: db_name='{Columns.AMOUNT.db_name}', display_name='{Columns.AMOUNT.display_name}'")
        
        # Test column groups
        core_columns = Columns.get('core_transaction')
        print(f"✓ Found {len(core_columns)} core transaction columns")
        
        # Test display mapping
        db_names = ['date', 'details', 'amount']
        display_mapping = Columns.get_display_mapping(db_names)
        print(f"✓ Display mapping: {display_mapping}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing Columns system: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_imports():
    """Test database-related imports."""
    print("\nTesting database imports...")
    
    try:
        from fm.core.data_services import DBIOService
        print("✓ Successfully imported DBIOService")
        
        from fm.core.database.sql_repository.transaction_repository import Transaction
        print("✓ Successfully imported Transaction")
        
        # Test creating a service instance
        service = DBIOService()
        print("✓ Successfully created DBIOService instance")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing database imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_imports():
    """Test CLI imports."""
    print("\nTesting CLI imports...")
    
    try:
        from fm.cli.db_cli import main, get_transactions, show_stats
        print("✓ Successfully imported CLI functions")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing CLI imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("Column System Refactoring Test")
    print("=" * 50)
    
    tests = [
        test_new_columns_system,
        test_database_imports,
        test_cli_imports,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("✓ Column system refactoring completed successfully!")
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        print("✗ Some issues need to be resolved")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
