# Configuration System Overview

The Flatmate application uses a two-layer configuration system designed for flexibility, type safety, and maintainability.

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    User Preferences                        │
│                (~/.flatmate/preferences.yaml)              │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                  Core Config Layer                         │
│              (Global Singleton Manager)                    │
│  • SystemPaths  • UserPaths  • ConfigManager              │
│  • UserPreferences  • Environment                         │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                 Local Config Layer                         │
│              (Component Abstractions)                      │
│  • BaseLocalConfig  • Type-safe keys                      │
│  • Component hierarchy  • Event integration               │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    Components                              │
│  • GuiConfig  • UpdateDataConfig  • HomeConfig           │
│  • Custom component configs                               │
└─────────────────────────────────────────────────────────────┘
```

## Core Principles

### 1. Separation of Concerns
- **Core Config:** Handles global settings, persistence, and system-wide configuration
- **Local Config:** Handles component-specific settings, defaults, and type safety

### 2. Configuration Hierarchy
Every configuration value follows this precedence order:
1. **User Preferences** (highest) - `~/.flatmate/preferences.yaml`
2. **Component Defaults** (middle) - `component/config/defaults.yaml`
3. **Hardcoded Defaults** (lowest) - defined in component code

### 3. Type Safety
- Components use enum-based keys for type-safe configuration access
- Generic typing ensures compile-time checking of key types
- No magic strings - all configuration keys are defined as enums

### 4. Event Integration
- Configuration changes can trigger events through the global event bus
- Components can react to configuration changes in real-time
- Supports reactive UI updates and system reconfiguration

## Directory Structure

```
fm/core/config/
├── README.md              # Core config documentation
├── config.py              # Core singleton config manager
├── base_local_config.py   # Local config abstraction
├── keys.py                # Centralized config key definitions
└── paths.py               # Path management utilities

docs/config_system/
├── README.md              # This overview document
├── base_local_config.md   # BaseLocalConfig documentation
└── config_serialization_fix_summary.md  # YAML serialization details

components/*/config/
├── *_config.py            # Component-specific config class
├── *_keys.py              # Component-specific key definitions
└── defaults.yaml          # Component default values (optional)
```

## Usage Patterns

### For Application Developers

**Getting global configuration:**
```python
from fm.core.config.config import config
from fm.core.config.keys import ConfigKeys

# Get system paths
data_dir = config.get_path(ConfigKeys.Paths.DATA)

# Get user preferences
window_height = config.get_pref(ConfigKeys.Window.HEIGHT, default=600)

# Set global values
config.set_value('app.debug_mode', True)
```

**Creating component configuration:**
```python
from fm.core.config.base_local_config import BaseLocalConfig

class MyComponentConfig(BaseLocalConfig[MyKeys]):
    def get_defaults(self) -> dict:
        return {
            MyKeys.SETTING_ONE: 'default_value',
            MyKeys.SETTING_TWO: True
        }

# Use in component
config = MyComponentConfig()
value = config.get_value(MyKeys.SETTING_ONE)
```

### For Component Developers

1. **Define your keys** with proper namespacing
2. **Create a config class** inheriting from BaseLocalConfig
3. **Implement get_defaults()** with sensible fallbacks
4. **Use type-safe key access** throughout your component
5. **Respect the configuration hierarchy** - don't override user preferences

## Key Files

### [config.py](../../src/fm/core/config/config.py)
The heart of the configuration system. Contains:
- **ConfigManager:** Main singleton providing unified config access
- **UserPreferences:** Handles loading/saving user preferences with YAML serialization
- **SystemPaths/UserPaths:** Platform-independent path management
- **Environment:** Environment-specific settings management

### [base_local_config.py](../../src/fm/core/config/base_local_config.py)
Component configuration abstraction. Provides:
- **Type-safe configuration access** for components
- **Automatic hierarchy handling** (defaults → YAML → preferences)
- **Event bus integration** for configuration changes
- **Standardized configuration pattern** across all components

### [keys.py](../../src/fm/core/config/keys.py)
Centralized configuration key definitions:
- **ConfigKeys:** Main configuration key categories
- **Organized by domain:** Window, Logging, Reports, UpdateData, etc.
- **Type-safe enum-based keys** prevent typos and enable IDE support

## Configuration Flow

### Application Startup
1. **Core config initializes** - loads system paths and environment
2. **User preferences load** - from `~/.flatmate/preferences.yaml`
3. **Components initialize** - each loads its own defaults and YAML files
4. **Configuration hierarchy resolves** - user prefs override component defaults
5. **Application starts** with fully resolved configuration

### Runtime Configuration Changes
1. **User modifies setting** in UI or preferences file
2. **Config system updates** internal state
3. **Events are emitted** to notify interested components
4. **Components react** to configuration changes
5. **Changes are persisted** to preferences file

### Component Configuration Loading
1. **Component creates config instance** (inherits from BaseLocalConfig)
2. **Hardcoded defaults loaded** from `get_defaults()`
3. **Component YAML loaded** from `component/config/defaults.yaml` (if exists)
4. **User preferences applied** from global preferences file
5. **Final configuration available** with proper hierarchy

## Best Practices

### Configuration Design
- **Use descriptive key names** with proper namespacing (e.g., `gui.window.height`)
- **Provide sensible defaults** for all configuration values
- **Group related settings** into logical categories
- **Document configuration options** and their effects

### Component Integration
- **Always inherit from BaseLocalConfig** for component configuration
- **Use enum-based keys** rather than string literals
- **Respect user preferences** - don't override them programmatically
- **Handle missing configuration gracefully** with appropriate defaults

### Performance Considerations
- **Configuration loading is lazy** - only loaded when first accessed
- **Caching is automatic** - repeated access is fast
- **File watching is not implemented** - changes require restart
- **Keep configuration simple** - avoid deep nesting and complex structures

## Migration and Refactoring

### Recent Changes (2025-06-15)
- **BaseModuleConfig → BaseLocalConfig:** Renamed for clarity (not just modules)
- **Location moved:** `modules/base/config/` → `core/config/` for logical grouping
- **Imports updated:** All consuming components updated to new location
- **Documentation created:** Comprehensive docs for the entire system

### Lessons Learned
- **Don't break singleton patterns** - core config must remain singleton
- **Working > Perfect** - incremental improvements beat risky architectural changes
- **Component isolation is valuable** - local config layer serves a real purpose
- **Type safety matters** - enum-based keys prevent many configuration errors

## Troubleshooting

### Common Issues
1. **Import errors:** Check that imports use new `core.config.base_local_config` path
2. **Configuration not loading:** Verify `get_defaults()` implementation and YAML syntax
3. **Type errors:** Ensure enum keys are properly defined as string enums
4. **Preferences not saving:** Check file permissions and YAML serialization

### Debug Tips
- **Configuration loading is silent** except for actual errors
- **Check application logs** for "Error processing config key" messages
- **Use `config.get_value()`** to verify values are set correctly
- **Test with minimal configuration** first, then add complexity

### Getting Help
- Check the [BaseLocalConfig documentation](base_local_config.md) for component config
- Review the [Core Config README](../../src/fm/core/config/README.md) for system details
- Look at existing implementations (GuiConfig, UpdateDataConfig) for examples
- Check the [Config Serialization Fix](config_serialization_fix_summary.md) for YAML issues

## Future Considerations

### Potential Improvements
- **File watching:** Automatic reload when configuration files change
- **Validation:** Schema validation for configuration values
- **Migration:** Automatic migration of old configuration formats
- **Documentation:** Auto-generated documentation from configuration schemas

### Architectural Stability
The current two-layer architecture is stable and should not be changed without careful consideration:
- **Core config singleton pattern is critical** - changing it breaks everything
- **Local config abstraction is valuable** - provides component isolation and type safety
- **Configuration hierarchy works well** - users can override defaults as expected
- **Event integration is sufficient** - supports reactive updates where needed
