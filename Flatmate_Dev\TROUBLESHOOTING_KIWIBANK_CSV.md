# Kiwibank CSV Processing Troubleshooting Guide

## Issue Description
When processing Kiwibank CSV files through the application UI, no data is processed, while tests pass successfully.

## Key Code Locations

### 1. Test Implementation
- **File**: `tests/test_dw_pipeline.py`
  - `PipelineTest` class runs end-to-end tests
  - Calls `dw_director` directly with test files

### 2. Application Implementation
- **File**: `flatmate/src/fm/modules/update_data/ud_presenter.py`
  - `UpdateDataPresenter` class handles UI interactions
  - `_handle_process` method calls `dw_director`

### 3. Director
- **File**: `flatmate/src/fm/modules/update_data/utils/dw_director.py`
  - `dw_director`: Main orchestration function
  - `_load_and_process_files`: Processes files using handlers

### 4. Statement Handlers
- **Base Handler**: `flatmate/src/fm/modules/update_data/utils/statement_handlers/_base_statement_handler.py`
  - `StatementHandler` base class with core processing logic
  - `process_file` method orchestrates the processing

- **Kiwibank Handler**: `flatmate/src/fm/modules/update_data/utils/statement_handlers/kiwibank_full_csv_handler.py`
  - Handles Kiwibank CSV format
  - Defines column mappings and parsing rules

## Debugging Steps

### 1. Verify File Reading
- [ ] Add logging in `_read_csv` method of `StatementHandler`
- [ ] Check file encoding and BOM handling
- [ ] Verify file paths are correct in application context

### 2. Check Handler Selection
- [ ] Add logging in `get_handler` function
- [ ] Verify the correct handler is being selected for Kiwibank files
- [ ] Check file extension and content matching

### 3. Inspect Processing Flow
- [ ] Add debug logs in `process_file` method
- [ ] Check for empty or None returns from processing steps
- [ ] Verify column mappings match the actual CSV structure

### 4. Test with Known Good File
1. Use a file that works in tests
2. Process it through the application UI
3. Compare the file paths and processing context

## Common Issues

### 1. File Path Issues
- Relative vs absolute paths
- Permission issues
- File encoding problems

### 2. Handler Configuration
- Incorrect column mappings
- Wrong date formats
- Missing required columns

### 3. Processing Errors
- Silent failures in data transformation
- Validation errors not being reported
- Empty data being filtered out

## Logging
Add these log statements to help diagnose:

```python
# In _read_csv method
Logger.debug(f"Reading file: {filepath}")
Logger.debug(f"First 5 lines: {df.head().to_string()}")

# In process_file
Logger.debug(f"Processing file: {filepath}")
Logger.debug(f"DataFrame shape before processing: {df.shape}")
```

## Testing

### Run Specific Test
```bash
# From project root
flatmate/.venv_fm313/Scripts/python.exe tests/test_dw_pipeline.py
```

### Debug Mode
Set environment variable for more verbose logging:
```bash
set LOG_LEVEL=DEBUG
```

## Next Steps
1. Add debug logging to identify where processing fails
2. Compare test and application file handling
3. Check for environment-specific issues

---
Last updated: 2025-07-11
