2025-07-15 02:29:42 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 02:29:44 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 5 user preferences
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_start_date = None (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_end_date = None (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_start_date = 2025-06-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_start_date = 2025-06-15
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_end_date = 2025-07-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_end_date = 2025-07-15
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_account = test_account_123 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_account = test_account_123
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.filters.default_days_back = 30 (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.filters.use_default_date_range = True (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.default_sort_column = amount to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.default_sort_column = amount
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.default_sort_order = ascending to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:29:44 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.default_sort_order = ascending
