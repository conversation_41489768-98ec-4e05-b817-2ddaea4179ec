# Database Configuration Issue Report

**Date:** 2025-07-10  
**Status:** Investigating  
**Priority:** High  
**Affected Modules:** `update_data`, `categorize`

## Issue Summary
- `update_data` module successfully updates the database
- `categorize` module reports database as empty
- Both modules should use the same database at `~/.flatmate/data/transactions.db`

## Investigation

### 1. Database Access Patterns

#### Update Data Module
- Uses `DBIOService` with `SQLiteTransactionRepository`
- Database path: Retrieved via `config.get_path(ConfigKeys.Paths.DATA) / 'transactions.db'`
- Successfully writes transactions

#### Categorize Module
- Also uses `DBIOService`
- Reports empty database
- May use different database instance or path

### 2. Configuration
- **Config Class:** `fm.core.config.config.ConfigManager`
- **Expected DB Path:** `~/.flatmate/data/transactions.db`
- **Repository:** `SQLiteTransactionRepository` in `fm.core.database.sql_repository`

## Investigation Findings

### Database Access
- **update_data Module**: Successfully writes transactions to the database
- **categorize Module**: Reports no transactions found when querying the same database

### Configuration
- Both modules use the same configuration system to determine the database path
- The database path is resolved from the core config using `ConfigKeys.Paths.DATA`
- The expected database location is `~/.flatmate/data/transactions.db`

### Data Flow Analysis
1. **UI Flow**:
   - User clicks "Load Data" in the categorize module
   - `LeftPanelWidget` emits `load_db_requested` signal with filters
   - `CategorizePresenter._handle_load_db` processes the request

2. **Data Loading**:
   - `CategorizePresenter` calls `DBIOService.list_transactions()` with filters
   - `DBIOService` uses `SQLiteTransactionRepository` to query the database
   - Repository executes SQL query and converts results to `Transaction` objects
   - Objects are converted to DataFrame using `convert_transactions_to_dataframe`

3. **Date Column Handling**:
   - The date column is stored as TEXT in SQLite (standard SQLite behavior)
   - This is parsable but requires explicit conversion to datetime in pandas
   - Current implementation may need explicit date parsing during DataFrame conversion

### Debug Findings
- Added extensive logging throughout the data flow path
- Logging tracks:
  - Filter parameters used in queries
  - Exact SQL queries and parameters
  - Database file path being accessed
  - Number of rows returned
  - DataFrame conversion process
  - Sample data at each stage

## Verification Steps

### 1. Check Database Location
```bash
python -c "from fm.core.config.config import config, ConfigKeys; print(config.get_path(ConfigKeys.Paths.DATA) / 'transactions.db')"
```

### 2. Verify Database File
```bash
# Windows
dir "%USERPROFILE%\.flatmate\data\transactions.db"

# Or check if file exists
python -c "import os; print(os.path.exists(os.path.expanduser('~/.flatmate/data/transactions.db')))"
```

### 3. Check Database Contents
```python
import sqlite3
from fm.core.config.config import config

from fm.core.config.keys import ConfigKeys
db_path = config.get_path(ConfigKeys.Paths.DATA) / 'transactions.db'
if db_path.exists():
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # List tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    print("Tables:", cursor.fetchall())
    
    # Count transactions
    cursor.execute("SELECT COUNT(*) FROM transactions")
    print("Transaction count:", cursor.fetchone()[0])
else:
    print("Database file not found at:", db_path)
```

## Recommended Fixes

### 1. Standardize Database Access
```python
# In any module that needs database access
from fm.core.config.config import config
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.data_services.db_io_service import DBIOService

# Always get database path from config
db_path = str(config.get_path('data') / 'transactions.db')
repo = SQLiteTransactionRepository(db_path=db_path)
db_service = DBIOService(repo=repo)
```

### 2. Add Debug Logging
```python
# In SQLiteTransactionRepository.__init__
print(f"[DEBUG] Database path: {self.db_path}")
print(f"[DEBUG] File exists: {self.db_path.exists()}")
```

## Next Steps
1. [ ] Run verification steps to confirm database location
2. [ ] Check application logs for database errors
3. [ ] Add debug logging to track database access
4. [ ] Test with a clean database

## Related Files
- `fm/core/config/config.py`
- `fm/core/database/sql_repository/sqlite_repository.py`
- `fm/core/data_services/db_io_service.py`
- `fm/modules/update_data/`
- `fm/modules/categorize/`
