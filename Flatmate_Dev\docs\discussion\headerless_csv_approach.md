# Headerless CSV Processing Approach

## Current Implementation

Currently, the statement handler supports both header and headerless CSV files through the `colnames_in_header` flag. This leads to complex logic in `_format_df` to handle both cases.
- Most problematic, it makes integer row indexing ambiguous and error-prone. with headers row zero = actual data row 1
without headers row 0 = header row.

## Proposed Simplification: Always Use Headerless

If we modify the system to always read CSVs without headers, the `_format_df` method would be simplified to:

```python
def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
    """
    Standardizes the format of the input DataFrame using headerless approach.
    
    This method assumes the input DataFrame has no meaningful column headers and
    replaces them with the standardized column names defined in the handler's
    configuration. This approach provides consistent column naming regardless of
    the source file's format.
    
    Args:
        df: Input DataFrame with either no headers or headers to be replaced
        
    Returns:
        DataFrame with standardized column names and cleaned data
    """
    # Extract account number before any modifications to the DataFrame
    account_number = self._extract_account_number(df)

    # 1. Slice DataFrame to exclude metadata rows if needed
    #    - If data_start_row > 0, we slice the DataFrame to start from that row

    if self.columns.data_start_row > 0:
        df = df.iloc[self.columns.data_start_row:].reset_index(drop=True)

    # 2. Standardize column names
    #    This handles both the initial column naming and any necessary standardization
    #    The method will use source_col_names and target_col_names from the handler's config
    df = self._standardize_columns(df)

    # 3. Clean and prepare the DataFrame
    
    # Remove any completely empty rows (where all values are NaN)
    # This helps clean up any blank lines that might have been in the original file
    df.dropna(how='all', inplace=True)
    
    # Add source filename as a column for tracking and debugging
    # This helps identify which file each row came from in the final output
    df[str(Columns.SOURCE_FILENAME)] = os.path.basename(self._current_filepath)

    # If the handler is configured to not have an account column in the data,
    # add the account number we extracted earlier to every row
    if not self.columns.has_account_column:
        df[str(Columns.ACCOUNT)] = account_number if account_number else ''

    # Standardize date formats across all date columns
    # This ensures consistent date handling regardless of source format
    df = self._standardize_dates(df)
    
    # Apply any bank-specific formatting overrides
    # This allows custom handling for specific bank formats if needed
    df = self._custom_format(df)

    # Remove any columns marked as empty in the configuration
    # These are typically used as placeholders in the source data
    if str(Columns.EMPTY_COLUMN) in df.columns:
        df = df.drop(columns=[str(Columns.EMPTY_COLUMN)])

    # If configured, concatenate specified columns into a details field
    # This is useful for combining multiple columns (like memo/description) into one
    if self.columns.concat_cols_for_details:
        self._create_details_column(df, self.columns.concat_cols_for_details)

    return df
```

## Proposed Core Logic Changes

To enforce a consistent headerless approach, we will simplify the core file reading and formatting methods.

**1. Always Read Headerless (`_read_csv`)**

The `_read_csv` method will be simplified to always use `header=None`, ensuring every file is initially read into a DataFrame without headers.

```python
def _read_csv(self, filepath: str, nrows: Optional[int] = None) -> Optional[pd.DataFrame]:
    """Centralized method to read CSV files as headerless."""
    col_attrs = self.columns
    try:
        df = pd.read_csv(
            filepath,
            header=None, # Always read without headers
            nrows=nrows,
            on_bad_lines='skip',
            engine='python',
            encoding=col_attrs.file_encoding,
            sep=','
        )
        return df
    except (FileNotFoundError, pd.errors.EmptyDataError):
        return None # Return None for non-existent or empty files
    except Exception as e:
        Logger.error(f"Unexpected error reading {filepath}: {e}")
        return None
```

**2. Simplify DataFrame Formatting (`_format_df`)**

With the reading logic now consistent, `_format_df` no longer needs conditional slicing. It can reliably slice from `data_start_row` for all files.

```python
def _format_df(self, df: pd.DataFrame) -> pd.DataFrame:
{{ ... }}
```

## Changes to Handler Configuration

Handlers would need to explicitly define all column names:

```python
class KiwibankFullCSVHandler(StatementHandler):
    statement_type = StatementType(
        bank_name="Kiwibank",
        variant="full",
        file_type="csv"
    )
    
    columns = ColumnAttributes(
        has_col_names=True,         # File has column names in first row
        col_names_in_header=True,  #with headerless reading this means the header row is row 0
        col_names_row=0,   # Validate header names
        data_start_row=1,           # Data starts after header
        has_account_column=True,    # Account number is in source data
        source_col_names=[
            "Account number",
            "Date",
            "Memo/Description",
            "Source Code (payment type)",
            "TP ref",
            "TP part",
            "TP code",
            "OP ref",
            "OP part",
            "OP code",
            "OP name",
            "OP Bank Account Number",
            "Amount (credit)",
            "Amount (debit)",
            "Amount",
            "Balance"
        ],
        # ... rest of config
    )
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardizes DataFrame columns based on handler configuration.
        
        For headerless files, assigns target column names directly and ensures
        column counts match the expected number of source columns.
        """
        # Convert target column objects to their string representations
        target_columns = [str(column) for column in self.columns.target_col_names]
        expected_column_count = len(target_columns)
        
        # Verify the DataFrame has at least the expected number of columns
        if df.shape[1] < expected_column_count:
            raise ValueError(
                f"Expected at least {expected_column_count} columns, "
                f"but found {df.shape[1]} columns in the input data"
            )
            
        # Slice to keep only the columns we expect (in case there are extras)
        df = df.iloc[:, :expected_column_count]
        
        # Assign the target column names to the DataFrame
        df.columns = target_columns
        
        return df
    
    @staticmethod
    def _preview_csv(filepath: str, encoding: str, header_row: int, nrows_to_read: int) -> Optional[pd.DataFrame]:
        """Reads a preview of a CSV file for validation purposes."""
        try:
            return pd.read_csv(
                filepath,
                encoding=encoding,
                header=header_row,
                nrows=nrows_to_read,
                skip_blank_lines=True,
            )
        except (FileNotFoundError, pd.errors.EmptyDataError, IndexError):
            return None
        except Exception as e:
            Logger.debug(f"Failed to read preview of {filepath}: {e}")
            return None

    @classmethod
    def can_handle_file(cls, filepath: str) -> bool:
        """Check if the handler can process the given file based on its configuration."""
        col_attrs = cls.columns

        # For files with headers, we identify them by checking if the expected
        # column names are present in the correct row.
        if not col_attrs.has_col_names or not col_attrs.source_col_names:
            return False # Cannot identify by columns

        df_preview = cls._preview_csv(
            filepath,
            encoding=col_attrs.file_encoding,
            header_row=col_attrs.col_names_row,
            nrows_to_read=1 # Only need to read the header
        )

        if df_preview is None:
            return False

        actual_headers = {str(h).strip() for h in df_preview.columns}
        expected_headers = {str(h).strip() for h in col_attrs.source_col_names}

        if not expected_headers.issubset(actual_headers):
            return False

        # If we've passed all checks, the handler is likely appropriate.
        return True
```

## Simplified Column Handling Approach

### Key Decisions

1. **Always Read as Headerless**
   - All CSV files are read with `header=None`
   - Eliminates ambiguity about row indexing
   - Makes behavior consistent across all handlers

2. **Target Column Names Define Structure**
   - `target_col_names` specify both the column names and their order
   - Concrete handlers define the exact column mapping
   - No need for `source_col_names` or header verification

3. **Simplified Processing**
   - Extract account number from metadata (if available)
   - Slice to `data_start_row` to skip headers/metadata
   - Apply `target_col_names` to the data

### Benefits

1. **Consistent Behavior**
   - Row 0 always means the same thing (first data row)
   - No conditional logic for header handling
   - Predictable column ordering

2. **Reduced Complexity**
   - Removes unnecessary column mapping logic
   - Eliminates special cases for header rows
   - Fewer edge cases to test and maintain

3. **Better Performance**
   - Single pass through the data
   - No string operations on column names
   - Less memory usage from intermediate DataFrames

### Implementation

```python
def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
    """Apply standard column names to the DataFrame."""
    target_names = [str(col) for col in self.columns.target_col_names]
    return df.iloc[:, :len(target_names)].set_axis(target_names, axis=1)
```

1. Unclear how `source_col_names` is used when `colnames_in_header=False`
2. Potential confusion between verification vs. renaming purposes
3. Need to document how empty/missing columns are handled

## Benefits

1. **Simpler Code**: No conditional logic for header handling
2. **More Predictable**: Consistent behavior across all file types
3. **Easier Debugging**: Column names always come from configuration
4. **Better Validation**: Can validate expected columns immediately

## Trade-offs

1. **Less Flexible**: Can't easily handle files with different column orders
2. **More Configuration**: Each handler needs explicit column names
3. **Manual Updates**: Need to update handlers if source file format changes
