#!/usr/bin/env python3
"""Test the enhanced database query functionality.

This tests:
1. Auto-loading from database on startup
2. Last query/filter persistence
3. Default sorting application
4. Filter saving and restoration
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

def test_database_enhancement():
    """Test the enhanced database query functionality."""
    print("🧪 Testing Database Query Enhancement")
    print("=" * 50)
    
    try:
        from fm.modules.categorize.config import config
        print("✅ Successfully imported categorize config")
        
        # Test 1: Check default database loading config
        print("\n📋 Test 1: Database Loading Defaults")
        config.ensure_defaults({
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.auto_load_on_startup': True,
            'categorize.database.default_sort_column': 'date',
            'categorize.database.default_sort_order': 'descending',

            # Define keys for last used filters (will be None initially)
            'categorize.database.last_start_date': None,
            'categorize.database.last_end_date': None,
            'categorize.database.last_account': None,
        })
        
        defaults = {
            'default_load_from_db': config.get_value('categorize.database.default_load_from_db'),
            'remember_last_query': config.get_value('categorize.database.remember_last_query'),
            'auto_load_on_startup': config.get_value('categorize.database.auto_load_on_startup'),
            'default_sort_column': config.get_value('categorize.database.default_sort_column'),
            'default_sort_order': config.get_value('categorize.database.default_sort_order'),
        }
        
        print("Database loading defaults:")
        for key, value in defaults.items():
            print(f"  • {key}: {value}")
        
        # Test 2: Test filter persistence
        print("\n🔍 Test 2: Filter Persistence")
        
        # Simulate setting some filters
        test_start_date = datetime.now().date() - timedelta(days=30)
        test_end_date = datetime.now().date()
        test_account = "test_account_123"
        
        print(f"Setting test filters:")
        print(f"  • start_date: {test_start_date}")
        print(f"  • end_date: {test_end_date}")
        print(f"  • account: {test_account}")
        
        # Save as user preferences (simulating what the presenter would do)
        config.set_user_preference('categorize.database.last_start_date', test_start_date.isoformat())
        config.set_user_preference('categorize.database.last_end_date', test_end_date.isoformat())
        config.set_user_preference('categorize.database.last_account', test_account)
        
        # Test retrieval
        saved_start = config.get_user_preference('categorize.database.last_start_date')
        saved_end = config.get_user_preference('categorize.database.last_end_date')
        saved_account = config.get_user_preference('categorize.database.last_account')
        
        print(f"Retrieved saved filters:")
        print(f"  • start_date: {saved_start}")
        print(f"  • end_date: {saved_end}")
        print(f"  • account: {saved_account}")
        
        # Test 3: Test default filter generation
        print("\n🎯 Test 3: Default Filter Generation")
        config.ensure_defaults({
            'categorize.filters.default_days_back': 30,
            'categorize.filters.use_default_date_range': True,
        })
        
        # Simulate the default filter logic
        use_default_range = config.get_value('categorize.filters.use_default_date_range')
        days_back = config.get_value('categorize.filters.default_days_back')
        
        if use_default_range:
            default_end = datetime.now().date()
            default_start = default_end - timedelta(days=days_back)
            
            print(f"Default filters would be:")
            print(f"  • start_date: {default_start} ({days_back} days back)")
            print(f"  • end_date: {default_end} (today)")
        
        # Test 4: Test sorting preferences
        print("\n📊 Test 4: Sorting Preferences")
        sort_column = config.get_value('categorize.database.default_sort_column')
        sort_order = config.get_value('categorize.database.default_sort_order')
        
        print(f"Default sorting:")
        print(f"  • column: {sort_column}")
        print(f"  • order: {sort_order}")
        
        # Test user can override sorting
        print("\nTesting sort customization...")
        config.set_user_preference('categorize.database.default_sort_column', 'amount')
        config.set_user_preference('categorize.database.default_sort_order', 'ascending')
        
        new_sort_column = config.get_value('categorize.database.default_sort_column')
        new_sort_order = config.get_value('categorize.database.default_sort_order')
        
        print(f"Customized sorting:")
        print(f"  • column: {new_sort_column}")
        print(f"  • order: {new_sort_order}")
        
        # Test 5: Show all database-related config
        print("\n📋 Test 5: All Database Config Keys")
        all_keys = config.list_available_keys()
        db_keys = {k: v for k, v in all_keys.items() if 'database' in k or 'filters' in k}
        
        print(f"Database-related config keys ({len(db_keys)}):")
        for key, value in db_keys.items():
            user_override = config.get_user_preference(key)
            if user_override is not None:
                print(f"  • {key}: {value} (USER: {user_override})")
            else:
                print(f"  • {key}: {value}")
        
        # Test 6: Export database preferences
        print("\n📄 Test 6: Export Database Preferences")
        prefs_yaml = config.export_user_preferences()
        
        # Show just the database-related parts
        lines = prefs_yaml.split('\n')
        db_lines = []
        in_db_section = False
        
        for line in lines:
            if line.startswith('database:') or line.startswith('filters:'):
                in_db_section = True
            elif line and not line.startswith(' ') and not line.startswith('#'):
                in_db_section = False
            
            if in_db_section or line.startswith('#'):
                db_lines.append(line)
        
        print("Database-related user preferences:")
        for line in db_lines[:15]:  # Show first 15 lines
            print(f"  {line}")
        
        print("\n✅ Database enhancement tests completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database enhancement tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_presenter_integration():
    """Test how the presenter would use these enhancements."""
    print("\n" + "=" * 50)
    print("🎯 Testing Presenter Integration")
    print("=" * 50)
    
    try:
        # This would normally be done by the presenter
        print("1. 🏗️  Simulating presenter initialization...")
        
        from fm.modules.categorize.config import config
        
        # Simulate what initialize() would do
        config.ensure_defaults({
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.auto_load_on_startup': True,
        })
        
        should_auto_load = config.get_value('categorize.database.auto_load_on_startup')
        load_from_db = config.get_value('categorize.database.default_load_from_db')
        
        print(f"   should_auto_load: {should_auto_load}")
        print(f"   load_from_db: {load_from_db}")
        
        if should_auto_load and load_from_db:
            print("   ✅ Would auto-load from database")
        else:
            print("   ❌ Would not auto-load")
        
        print("\n2. 🔍 Simulating filter restoration...")
        
        # Check for last used filters
        last_start = config.get_user_preference('categorize.database.last_start_date')
        last_end = config.get_user_preference('categorize.database.last_end_date')
        last_account = config.get_user_preference('categorize.database.last_account')
        
        if last_start or last_end or last_account:
            print("   ✅ Found last used filters:")
            if last_start:
                print(f"     • start_date: {last_start}")
            if last_end:
                print(f"     • end_date: {last_end}")
            if last_account:
                print(f"     • account: {last_account}")
        else:
            print("   ℹ️  No last used filters, would use defaults")
        
        print("\n✅ Presenter integration test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Presenter integration test failed: {e}")
        return False

if __name__ == "__main__":
    success1 = test_database_enhancement()
    success2 = test_presenter_integration()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 Database query enhancement working perfectly!")
        print("📋 Key improvements:")
        print("  ✅ Auto-loads from database on startup")
        print("  ✅ Remembers last used query/filters")
        print("  ✅ Applies default sorting")
        print("  ✅ Saves filters for next session")
        print("🔧 Ready for Sprint 2.2: Filter UI Improvements")
    else:
        print("🔧 Issues found - need to investigate further")
    
    sys.exit(0 if (success1 and success2) else 1)
