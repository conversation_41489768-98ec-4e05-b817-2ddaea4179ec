"""Categorize module presenter (MVP stub).

Responsibilities:
* Instantiate the CatView
* Delegate file selection and categorisation to core.processor
* Handle navigation back to home or other modules
* Wire view signals to internal handlers
"""
from __future__ import annotations
from pathlib import Path
from typing import List
from datetime import datetime

import pandas as pd

from fm.core.data_services import DBIOService
from .core.categorizer import TransactionCategorizer
from ._view.cat_view import CatView
from ...core.services.logger import log
from ...core.services.cache_service import cache_service
from .utils.transaction_utils import transactions_to_dataframe

class CategorizePresenter:
    """Presenter for the Categorize module (stub version)."""

    def __init__(self, main_window):
        self.main_window = main_window
        self.view = CatView(main_window)
        self.data_service = DBIOService()
        self._cat = TransactionCategorizer()
        self._connect_signals()
        self._original_df = None
        self._modified = False

    # ------------------------------------------------------------------
    def _connect_signals(self):
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.files_selected.connect(self._handle_files_selected)
        self.view.load_db_requested.connect(self._handle_load_db)
        self.view.transaction_selected.connect(self._handle_transaction_selected)
        self.view.tags_updated.connect(self._handle_tags_updated)

    # ------------------------------------------------------------------
    def initialize(self):
        """Setup view in the main window."""
        self.view.setup_in_main_window(self.main_window)
        self.main_window.show_left_panel()
        
        # Check for cached transactions
        if cache_service.has('categorize_transactions'):
            df = cache_service.get('categorize_transactions')
            self._original_df = df.copy()
            self.view.set_dataframe(df)

    # ------------------------------------------------------------------
    def _handle_files_selected(self, file_paths: List[str]):
        log(f"CategorizePresenter received {len(file_paths)} file(s)", level="info")
        df: pd.DataFrame = categorize_files(file_paths)
        self._original_df = df.copy()
        self.view.set_dataframe(df)
        self._modified = False

    # ------------------------------------------------------------------
    def _handle_load_db(self, filters=None):
        log("Loading transactions from DB for categorisation…", level="info")
        log(f"Filters: {filters}", level="debug")
        
        # Apply filters if provided
        filter_kwargs = {}
        if filters:
            if 'start_date' in filters:
                filter_kwargs['start_date'] = filters['start_date']
            if 'end_date' in filters:
                filter_kwargs['end_date'] = filters['end_date']
            if 'account' in filters:
                filter_kwargs['account_number'] = filters['account']
        
        log(f"Fetching transactions with filters: {filter_kwargs}", level="debug")
        
        try:
            # Get transactions from database (as Transaction objects)
            txns = self.data_service.list_transactions(**filter_kwargs)
            
            log(f"Retrieved {len(txns)} transactions from database", level="info")
            
            if not txns:
                log("No transactions found in database matching the criteria", level="warning")
                # Show empty dataframe with expected columns
                df = pd.DataFrame(columns=[
                    'date', 'details', 'amount', 'balance', 'account', 
                    'source_uid', 'category', 'tags', 'notes', 'is_processed'
                ])
                self.view.set_dataframe(df)
                return

            # Convert transactions to DataFrame with proper column names
            df = transactions_to_dataframe(txns)
            log(f"Converted to DataFrame with shape: {df.shape}", level="debug")
            
            if df.empty:
                log("DataFrame is empty after conversion from Transaction objects", level="error")
                return

            # Ensure required columns exist
            if "category" not in df.columns:
                df["category"] = ""
            if "tags" not in df.columns:
                log("Adding missing 'tags' column", level="debug")
                df["tags"] = ""
                
            # Apply categorization
            log("Applying categorization to transactions...", level="debug")
            df["category"] = df.apply(self._cat.categorize_row, axis=1)
            
            log(f"Setting DataFrame with {len(df)} transactions to view", level="debug")
            self._original_df = df.copy()
            self.view.set_dataframe(df)
            self._modified = False
            log("Successfully loaded and displayed transactions", level="info")
            
        except Exception as e:
            log(f"Error loading transactions from database: {str(e)}", level="error")
            raise

    # ------------------------------------------------------------------
    def _handle_transaction_selected(self, transaction_id):
        """Handle transaction selection."""
        # This could be used to show details in a separate panel
        log(f"Transaction selected: {transaction_id}")
        
    # ------------------------------------------------------------------
    def _handle_tags_updated(self, transaction_id, tags):
        """Handle tags update from the view."""
        if self._original_df is not None:
            # Find the row with this transaction ID
            idx = self._original_df.index[
                self._original_df['id'] == transaction_id
            ].tolist()
            
            if idx:
                # Update the tags in the DataFrame
                self._original_df.at[idx[0], 'tags'] = tags
                self._modified = True
                
                # Update in database
                self.data_service.update_transaction_tags(transaction_id, tags)

    # ------------------------------------------------------------------
    def request_transition(self, target_view: str):
        """Stub – replaced by ModuleCoordinator._connect_module_transitions"""
        pass

    # ------------------------------------------------------------------
    def cleanup(self):
        """Clean up resources before transitioning away."""
        # Cache current transactions if modified
        if self._modified and self._original_df is not None:
            cache_service.put('categorize_transactions', self._original_df)
            
        if self.view:
            self.view.cleanup()




