"""Categorize module presenter (MVP stub).

Responsibilities:
* Instantiate the CatView
* Delegate file selection and categorisation to core.processor
* Handle navigation back to home or other modules
* Wire view signals to internal handlers
"""
from __future__ import annotations
from pathlib import Path
from typing import List
from datetime import datetime

import pandas as pd

from fm.core.data_services import DBIOService
from .core.categorizer import TransactionCategorizer
from ._view.cat_view import CatView
from ...core.services.logger import log
from ...core.services.cache_service import cache_service
from .utils.transaction_utils import transactions_to_dataframe

class CategorizePresenter:
    """Presenter for the Categorize module (stub version)."""

    def __init__(self, main_window):
        self.main_window = main_window
        self.view = CatView(main_window)
        self.data_service = DBIOService()
        self._cat = TransactionCategorizer()
        self._connect_signals()
        self._original_df = None
        self._modified = False

    # ------------------------------------------------------------------
    def _connect_signals(self):
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.files_selected.connect(self._handle_files_selected)
        self.view.load_db_requested.connect(self._handle_load_db)
        self.view.transaction_selected.connect(self._handle_transaction_selected)
        self.view.tags_updated.connect(self._handle_tags_updated)

    # ------------------------------------------------------------------
    def initialize(self):
        """Setup view in the main window."""
        self.view.setup_in_main_window(self.main_window)
        self.main_window.show_left_panel()

        # Import config for database loading preferences
        from .config import config

        # Set up database loading defaults
        config.ensure_defaults({
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.auto_load_on_startup': True,
            'categorize.database.default_sort_column': 'date',
            'categorize.database.default_sort_order': 'descending',

            # Define keys for last used filters (will be None initially)
            'categorize.database.last_start_date': None,
            'categorize.database.last_end_date': None,
            'categorize.database.last_account': None,
        })

        # Check if we should auto-load from database
        should_auto_load = config.get_value('categorize.database.auto_load_on_startup')
        load_from_db = config.get_value('categorize.database.default_load_from_db')

        if should_auto_load and load_from_db:
            # Try to load with last used query/filters
            self._auto_load_from_database()
        else:
            # Fallback: Check for cached transactions
            if cache_service.has('categorize_transactions'):
                df = cache_service.get('categorize_transactions')
                self._original_df = df.copy()
                self.view.set_dataframe(df)

    # ------------------------------------------------------------------
    def _auto_load_from_database(self):
        """Auto-load from database using last query and filters."""
        from .config import config

        log("Auto-loading from database with last query/filters...", level="info")

        # Try to restore last used query/filters
        remember_last = config.get_value('categorize.database.remember_last_query')
        if remember_last:
            last_filters = self._get_last_used_filters()
            if last_filters:
                log(f"Restoring last used filters: {last_filters}", level="debug")
                self._handle_load_db(last_filters)
                return

        # No last query found, load with default filters
        default_filters = self._get_default_filters()
        log(f"Loading with default filters: {default_filters}", level="debug")
        self._handle_load_db(default_filters)

    def _get_last_used_filters(self):
        """Get the last used database query filters."""
        from .config import config

        # Try to get last used filters from config
        last_start_date = config.get_user_preference('categorize.database.last_start_date')
        last_end_date = config.get_user_preference('categorize.database.last_end_date')
        last_account = config.get_user_preference('categorize.database.last_account')

        if last_start_date or last_end_date or last_account:
            filters = {}
            if last_start_date:
                # Convert string back to date if needed
                if isinstance(last_start_date, str):
                    from datetime import datetime
                    last_start_date = datetime.fromisoformat(last_start_date).date()
                filters['start_date'] = last_start_date
            if last_end_date:
                if isinstance(last_end_date, str):
                    from datetime import datetime
                    last_end_date = datetime.fromisoformat(last_end_date).date()
                filters['end_date'] = last_end_date
            if last_account:
                filters['account'] = last_account
            return filters

        return None

    def _get_default_filters(self):
        """Get default database query filters."""
        from .config import config
        from datetime import datetime, timedelta

        # Set up default filter config
        config.ensure_defaults({
            'categorize.filters.default_days_back': 30,
            'categorize.filters.use_default_date_range': True,
        })

        filters = {}

        # Add default date range if configured
        use_default_range = config.get_value('categorize.filters.use_default_date_range')
        if use_default_range:
            days_back = config.get_value('categorize.filters.default_days_back')
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days_back)

            filters['start_date'] = start_date
            filters['end_date'] = end_date

        return filters

    def _save_last_used_filters(self, filters):
        """Save the current filters as last used for next time."""
        from .config import config

        remember_last = config.get_value('categorize.database.remember_last_query')
        if not remember_last:
            return

        # Save filters as user preferences
        if filters:
            if 'start_date' in filters and filters['start_date']:
                # Convert date to string for storage
                date_str = filters['start_date'].isoformat() if hasattr(filters['start_date'], 'isoformat') else str(filters['start_date'])
                config.set_user_preference('categorize.database.last_start_date', date_str)

            if 'end_date' in filters and filters['end_date']:
                date_str = filters['end_date'].isoformat() if hasattr(filters['end_date'], 'isoformat') else str(filters['end_date'])
                config.set_user_preference('categorize.database.last_end_date', date_str)

            if 'account' in filters and filters['account']:
                config.set_user_preference('categorize.database.last_account', filters['account'])

        log("Saved last used filters for next session", level="debug")

    # ------------------------------------------------------------------
    def _handle_files_selected(self, file_paths: List[str]):
        log(f"CategorizePresenter received {len(file_paths)} file(s)", level="info")
        df: pd.DataFrame = categorize_files(file_paths)
        self._original_df = df.copy()
        self.view.set_dataframe(df)
        self._modified = False

    # ------------------------------------------------------------------
    def _handle_load_db(self, filters=None):
        """Enhanced database loading with filter persistence and default sorting."""
        from .config import config

        log("Loading transactions from DB for categorisation…", level="info")
        log(f"Filters: {filters}", level="debug")

        # Save filters for next time if configured
        if filters:
            self._save_last_used_filters(filters)

        # Apply filters if provided
        filter_kwargs = {}
        if filters:
            if 'start_date' in filters:
                filter_kwargs['start_date'] = filters['start_date']
            if 'end_date' in filters:
                filter_kwargs['end_date'] = filters['end_date']
            if 'account' in filters:
                filter_kwargs['account_number'] = filters['account']

        log(f"Fetching transactions with filters: {filter_kwargs}", level="debug")
        
        try:
            # Get transactions from database (as Transaction objects)
            txns = self.data_service.list_transactions(**filter_kwargs)
            
            log(f"Retrieved {len(txns)} transactions from database", level="info")
            
            if not txns:
                log("No transactions found in database matching the criteria", level="warning")
                # Show empty dataframe with expected columns
                df = pd.DataFrame(columns=[
                    'date', 'details', 'amount', 'balance', 'account', 
                    'source_uid', 'category', 'tags', 'notes', 'is_processed'
                ])
                self.view.set_dataframe(df)
                return

            # Convert transactions to DataFrame with proper column names
            df = transactions_to_dataframe(txns)
            log(f"Converted to DataFrame with shape: {df.shape}", level="debug")
            
            if df.empty:
                log("DataFrame is empty after conversion from Transaction objects", level="error")
                return

            # Ensure required columns exist
            if "category" not in df.columns:
                df["category"] = ""
            if "tags" not in df.columns:
                log("Adding missing 'tags' column", level="debug")
                df["tags"] = ""
                
            # Apply categorization
            log("Applying categorization to transactions...", level="debug")
            df["category"] = df.apply(self._cat.categorize_row, axis=1)

            # Apply default sorting (addresses hit list issue)
            df = self._apply_default_sorting(df)

            log(f"Setting DataFrame with {len(df)} transactions to view", level="debug")
            self._original_df = df.copy()
            self.view.set_dataframe(df)
            self._modified = False
            log("Successfully loaded and displayed transactions", level="info")
            
        except Exception as e:
            log(f"Error loading transactions from database: {str(e)}", level="error")
            raise

    def _apply_default_sorting(self, df):
        """Apply default sorting to the DataFrame (addresses hit list issue)."""
        from .config import config

        # Get default sort preferences
        sort_column = config.get_value('categorize.database.default_sort_column')
        sort_order = config.get_value('categorize.database.default_sort_order')

        if sort_column and sort_column in df.columns:
            ascending = sort_order != 'descending'

            log(f"Applying default sort: {sort_column} ({'ascending' if ascending else 'descending'})", level="debug")

            try:
                # Handle date column sorting specially
                if sort_column == 'date' and 'date' in df.columns:
                    # Ensure date column is properly formatted for sorting
                    df['date'] = pd.to_datetime(df['date'], errors='coerce')

                df = df.sort_values(by=sort_column, ascending=ascending)
                log(f"Successfully sorted by {sort_column}", level="debug")

            except Exception as e:
                log(f"Error applying default sort: {e}", level="warning")
                # Continue without sorting rather than failing

        return df

    # ------------------------------------------------------------------
    def _handle_transaction_selected(self, transaction_id):
        """Handle transaction selection."""
        # This could be used to show details in a separate panel
        log(f"Transaction selected: {transaction_id}")
        
    # ------------------------------------------------------------------
    def _handle_tags_updated(self, transaction_id, tags):
        """Handle tags update from the view."""
        if self._original_df is not None:
            # Find the row with this transaction ID
            idx = self._original_df.index[
                self._original_df['id'] == transaction_id
            ].tolist()
            
            if idx:
                # Update the tags in the DataFrame
                self._original_df.at[idx[0], 'tags'] = tags
                self._modified = True
                
                # Update in database
                self.data_service.update_transaction_tags(transaction_id, tags)

    # ------------------------------------------------------------------
    def request_transition(self, target_view: str):
        """Stub – replaced by ModuleCoordinator._connect_module_transitions"""
        pass

    # ------------------------------------------------------------------
    def cleanup(self):
        """Clean up resources before transitioning away."""
        # Cache current transactions if modified
        if self._modified and self._original_df is not None:
            cache_service.put('categorize_transactions', self._original_df)
            
        if self.view:
            self.view.cleanup()




