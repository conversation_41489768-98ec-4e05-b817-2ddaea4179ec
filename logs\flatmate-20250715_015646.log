2025-07-15 01:56:46 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 01:56:47 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.header_height = 30 (Source: test_new_config.py:__init__)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.scroll_bar_width = 15 (Source: test_new_config.py:setup_layout)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.border_width = 1 (Source: test_new_config.py:setup_layout)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.date_width = 100 (Source: test_new_config.py:setup_columns)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.amount_width = 120 (Source: test_new_config.py:setup_columns)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.category_width = 150 (Source: test_new_config.py:setup_columns)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.default_sort_column = date (Source: test_new_config.py:configure_sorting)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.sort_ascending = False (Source: test_new_config.py:configure_sorting)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.table.allow_multi_sort = True (Source: test_new_config.py:configure_sorting)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.filters.show_advanced = False (Source: test_new_config.py:initialize)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.display.show_filter_panel = True (Source: test_new_config.py:initialize)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.data.page_size = 100 (Source: test_new_config.py:setup_categorize_presenter)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.data.auto_refresh_seconds = 30 (Source: test_new_config.py:setup_categorize_presenter)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.performance.lazy_load = True (Source: test_new_config.py:setup_categorize_presenter)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.shortcuts.save_key = Ctrl+S (Source: test_new_config.py:initialize_keyboard_shortcuts)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.shortcuts.filter_key = Ctrl+F (Source: test_new_config.py:initialize_keyboard_shortcuts)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.shortcuts.refresh_key = F5 (Source: test_new_config.py:initialize_keyboard_shortcuts)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.shortcuts.delete_key = Delete (Source: test_new_config.py:initialize_keyboard_shortcuts)
2025-07-15 01:56:48 - [fm.core.config.base_local_config_v2] [INFO] - Defaults YAML for categorize saved to: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
