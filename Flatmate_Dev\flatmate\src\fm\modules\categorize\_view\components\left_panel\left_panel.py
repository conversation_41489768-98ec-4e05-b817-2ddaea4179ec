"""Left panel for the Categorize module."""

from datetime import datetime, timed<PERSON><PERSON>
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QVBoxLayout, QGroupBox, QFormLayout,
    QDateEdit, QComboBox, QCheckBox
)

from fm.gui._shared_components.base.base_pane import BasePane
from fm.modules.categorize.config import config
from fm.gui._shared_components.widgets.option_menus import OptionMenuWithLabelAndButton
from fm.gui._shared_components.widgets.buttons import ExitButton, ActionButton,SecondaryButton


class LeftPanelWidget(BasePane):
    """Left panel widget for the Categorize module."""
    
    # Signals
    files_select_requested = Signal()
    load_db_requested = Signal()
    apply_filters_clicked = Signal(dict)
    cancel_clicked = Signal()
    
    def __init__(self, parent=None):
        """Initialize the left panel widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Ensure filter panel config defaults
        config.ensure_defaults({
            'categorize.filters.panel_min_width': 200,
            'categorize.filters.panel_max_width': 300,
            'categorize.filters.default_days_back': 365,  # Default to 1 year back
            'categorize.filters.remember_last_filter': True,
            'categorize.filters.show_calendar_popup': True
        })

        layout = QVBoxLayout(self)
        min_width = config.get_value('categorize.filters.panel_min_width')
        max_width = config.get_value('categorize.filters.panel_max_width')
        self.setMinimumWidth(min_width)
        self.setMaximumWidth(max_width)
        
        # Add load source option menu with button
        self.load_source_menu = OptionMenuWithLabelAndButton(
            label_text="Load from...",
            options=["database", "file"],
            button_text="Load Data"
        )
        # Set default to database
        self.load_source_menu.combo_box.setCurrentText("database")
        layout.addWidget(self.load_source_menu)
        
        # Add filter group
        self.filter_group = QGroupBox("Filter Transactions")
        self.filter_group.setCheckable(True)
        self.filter_group.setChecked(False)  # Default to not filtering
        filter_layout = QFormLayout(self.filter_group)
        
        # Date range - configurable defaults
        days_back = config.get_value('categorize.filters.default_days_back')
        show_calendar = config.get_value('categorize.filters.show_calendar_popup')

        self.date_from = QDateEdit()
        self.date_from.setCalendarPopup(show_calendar)
        # Set default start date based on config (days back from today)
        start_date = datetime.now().date() - timedelta(days=days_back)
        self.date_from.setDate(start_date)

        self.date_to = QDateEdit()
        self.date_to.setCalendarPopup(show_calendar)
        self.date_to.setDate(datetime.now().date())

        # Initially disable date widgets
        self.date_from.setEnabled(False)
        self.date_to.setEnabled(False)
        
        filter_layout.addRow("From:", self.date_from)
        filter_layout.addRow("To:", self.date_to)
        
        # Account selector
        self.account_combo = QComboBox()
        self.account_combo.addItem("All Accounts", None)
        filter_layout.addRow("Account:", self.account_combo)
        
        # Apply filters button
        self.apply_filters_btn = SecondaryButton("Apply Filters")
        filter_layout.addRow("", self.apply_filters_btn)
        
        layout.addWidget(self.filter_group)
        
        # Add spacer to push buttons to top
        layout.addStretch()
        
        # Add cancel button at bottom
        self.cancel_btn = ExitButton("Exit")
        layout.addWidget(self.cancel_btn)
    
    def _connect_signals(self):
        """Connect internal signals."""
        self.load_source_menu.button_clicked.connect(self._on_load_data_clicked)
        self.apply_filters_btn.clicked.connect(self._on_apply_filters)
        self.cancel_btn.clicked.connect(self.cancel_clicked)
        self.filter_group.toggled.connect(self._on_filter_group_toggled)
    
    def _on_load_data_clicked(self):
        """Handle load data button click based on selected option."""
        selected_option = self.load_source_menu.get_selected_option()
        if selected_option == "database":
            self.load_db_requested.emit()
        elif selected_option == "file":
            self.files_select_requested.emit()

    def _on_filter_group_toggled(self, checked):
        """Enable or disable filter widgets based on the checkbox state."""
        self.date_from.setEnabled(checked)
        self.date_to.setEnabled(checked)
        self.account_combo.setEnabled(checked)
        self.apply_filters_btn.setEnabled(checked)

    def _on_apply_filters(self):
        """Handle apply filters button click."""
        filters = {
            'start_date': self.date_from.date().toPython(),
            'end_date': self.date_to.date().toPython(),
            'account': self.account_combo.currentData()
        }
        self.apply_filters_clicked.emit(filters)
    
    def set_accounts(self, accounts):
        """Set the available accounts in the combo box."""
        self.account_combo.clear()
        self.account_combo.addItem("All Accounts", None)
        
        for account in accounts:
            self.account_combo.addItem(account, account)
    
    def get_filters(self):
        """Get the current filter settings."""
        if not self.filter_group.isChecked():
            return {}
            
        return {
            'start_date': self.date_from.date().toPython(),
            'end_date': self.date_to.date().toPython(),
            'account': self.account_combo.currentData()
        }
    
    def show_component(self):
        """Show this component."""
        self.show()
        self.activate()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
        self.deactivate()
