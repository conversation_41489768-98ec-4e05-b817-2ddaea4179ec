"""Left panel for the Categorize module."""

from datetime import datetime, timed<PERSON><PERSON>
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QVBoxLayout, QGroupBox, QFormLayout,
    QDateEdit, QComboBox, QCheckBox
)

from fm.gui._shared_components.base.base_pane import BasePane
from fm.modules.categorize.config import config
from fm.gui._shared_components.widgets.option_menus import OptionMenuWithLabelAndButton
from fm.gui._shared_components.widgets.buttons import ExitButton, ActionButton,SecondaryButton
from fm.gui._shared_components.widgets.date_filter_pane import DateFilterPane


class LeftPanelWidget(BasePane):
    """Left panel widget for the Categorize module."""
    
    # Signals
    files_select_requested = Signal()
    load_db_requested = Signal()
    apply_filters_clicked = Signal(dict)
    cancel_clicked = Signal()
    
    def __init__(self, parent=None):
        """Initialize the left panel widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Ensure filter panel config defaults
        config.ensure_defaults({
            'categorize.filters.panel_min_width': 200,
            'categorize.filters.panel_max_width': 300,
            'categorize.filters.default_days_back': 365,  # Default to 1 year back
            'categorize.filters.remember_last_filter': True,
            'categorize.filters.show_calendar_popup': True
        })

        layout = QVBoxLayout(self)
        min_width = config.get_value('categorize.filters.panel_min_width')
        max_width = config.get_value('categorize.filters.panel_max_width')
        self.setMinimumWidth(min_width)
        self.setMaximumWidth(max_width)
        
        # Add load source option menu with button
        self.load_source_menu = OptionMenuWithLabelAndButton(
            label_text="Load from...",
            options=["database", "file"],
            button_text="Load Data"
        )
        # Set default to database
        self.load_source_menu.combo_box.setCurrentText("database")
        layout.addWidget(self.load_source_menu)
        
        # Add enhanced date filter pane
        self.date_filter_pane = DateFilterPane()
        layout.addWidget(self.date_filter_pane)

        # Add account filter section
        self.account_filter_frame = QGroupBox("Account Filter")
        account_layout = QFormLayout(self.account_filter_frame)
        account_layout.setContentsMargins(8, 8, 8, 8)

        self.account_combo = QComboBox()
        self.account_combo.addItem("All Accounts", None)
        account_layout.addRow("Account:", self.account_combo)

        layout.addWidget(self.account_filter_frame)

        # Add filter action buttons
        self.apply_filters_btn = ActionButton("Apply Filters")
        self.clear_filters_btn = SecondaryButton("Clear All")

        layout.addWidget(self.apply_filters_btn)
        layout.addWidget(self.clear_filters_btn)
        
        # Add spacer to push buttons to top
        layout.addStretch()
        
        # Add cancel button at bottom
        self.cancel_btn = ExitButton("Exit")
        layout.addWidget(self.cancel_btn)
    
    def _connect_signals(self):
        """Connect internal signals."""
        self.load_source_menu.button_clicked.connect(self._on_load_data_clicked)
        self.apply_filters_btn.clicked.connect(self._on_apply_filters)
        self.clear_filters_btn.clicked.connect(self._on_clear_filters)
        self.cancel_btn.clicked.connect(self.cancel_clicked)

        # Connect date filter signals
        self.date_filter_pane.filter_changed.connect(self._on_date_filter_changed)
        self.date_filter_pane.preset_selected.connect(self._on_date_preset_selected)
    
    def _on_load_data_clicked(self):
        """Handle load data button click based on selected option."""
        selected_option = self.load_source_menu.get_selected_option()
        if selected_option == "database":
            self.load_db_requested.emit()
        elif selected_option == "file":
            self.files_select_requested.emit()

    def _on_date_filter_changed(self, filter_data):
        """Handle date filter changes."""
        # Store the current date filter for when Apply is clicked
        self._current_date_filter = filter_data

        # Auto-apply if configured (future enhancement)
        # For now, just store the filter data

    def _on_date_preset_selected(self, preset_id):
        """Handle date preset selection."""
        # Could show feedback or save preference here
        pass

    def _on_apply_filters(self):
        """Apply filters and emit signal."""
        filters = {}

        # Get date filter from the DateFilterPane
        date_filter = self.date_filter_pane.get_current_filter()
        if date_filter['start_date'] and date_filter['end_date']:
            filters['start_date'] = date_filter['start_date']
            filters['end_date'] = date_filter['end_date']

        # Add account filter if not "All Accounts"
        account_data = self.account_combo.currentData()
        if account_data is not None:
            filters['account'] = account_data

        self.apply_filters_clicked.emit(filters)

    def _on_clear_filters(self):
        """Clear all filters and reset to defaults."""
        # Reset date filter to "All"
        self.date_filter_pane.set_preset('all')

        # Reset account to "All Accounts"
        self.account_combo.setCurrentIndex(0)

        # Apply empty filters (which means no filtering)
        self.apply_filters_clicked.emit({})
    
    def set_accounts(self, accounts):
        """Set the available accounts in the combo box."""
        self.account_combo.clear()
        self.account_combo.addItem("All Accounts", None)
        
        for account in accounts:
            self.account_combo.addItem(account, account)
    
    def get_filters(self):
        """Get the current filter settings."""
        filters = {}

        # Get date filter from the DateFilterPane
        date_filter = self.date_filter_pane.get_current_filter()
        if date_filter['start_date'] and date_filter['end_date']:
            filters['start_date'] = date_filter['start_date']
            filters['end_date'] = date_filter['end_date']

        # Add account filter if not "All Accounts"
        account_data = self.account_combo.currentData()
        if account_data is not None:
            filters['account'] = account_data

        return filters
    
    def show_component(self):
        """Show this component."""
        self.show()
        self.activate()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
        self.deactivate()
