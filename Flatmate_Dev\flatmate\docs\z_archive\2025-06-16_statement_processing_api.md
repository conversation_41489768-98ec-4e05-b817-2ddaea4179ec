# Statement-Processing API & Design

_Last updated: 2025-06-16_

---
## 1  Pur<PERSON> & Scope
The Flatmate desktop app needs a **stable, minimal API** for processing bank-statement files that is shared by multiple modules (Update-Data, Categorise, future analytics).  This document defines that API and the component boundaries for the MVP while flagging future directions.

---
## 2  Key Use-Cases
The pipeline supports both **file ingestion** (Update-Data) and **database re-categorisation** (Categorise).

1. **Ad-hoc import** – User selects CSV/XLSX statement files, receives merged, cleaned `DataFrame` (Update-Data & Categorise).
2. **DB re-categorisation** – Load existing transactions from DB, run categoriser again.
3. **Batch/CLI backfill** – Headless processing of large archives.

---
## 3  Public API Surface (v0)
| Function | Module | Returns | Notes |
|----------|--------|---------|-------|
| `process_files(paths: list[str]) -> pd.DataFrame` | `fm.statement_processing` (shim) | Merged & deduped transactions | Convenience wrapper for UI modules. |
| `dw_director(job_sheet: dict) -> dict` | `update_data.utils.dw_director` | Rich result dict (stats, output path, db info) | Powers Update-Data “full pipeline” incl. backups & DB commit. |
| `categorise(df: pd.DataFrame) -> pd.DataFrame` | `categorize.core.categorizer` | Adds/updates `category` column | Pure function, no I/O. |

Future additions: `load_from_db(...)`, `export_master_file(...)`, etc.

---
## 4  Responsibility Split
```
fm/statement_processing/          # stable import point
    __init__.py   ← implements process_files (thin wrapper)

modules/update_data/utils/
    dw_pipeline.py  ← low-level helpers (load_csv_file, merge_dataframes, …)
    dw_director.py  ← orchestrates end-to-end job_sheet run

modules/categorize/core/
    categorizer.py  ← applies regex/ML rules to DataFrame
```

---
## 5  Data Contracts
* **Required columns**: Date, Amount, Description; optional Balance.  Names are standardised in handlers.
* `Date` column must be `datetime64[ns]`.  No invalid dates.
* Category column: `category` (string).  Blank if uncategorised.
* Each DataFrame carries attributes:
  * `filepath`: original source path
  * `filename`: basename for display

---
## 6  Error-Handling Strategy
* Helpers raise exceptions internally but top-level APIs **return dicts** with `status` / `message` for GUI friendliness.
* `process_files` raises on critical path errors (caller may show message box).

---
## 7  Future Extensions
* Swap regex categoriser for ML model (probabilistic).  Keep same column name.
* Move entire pipeline into independent `fm-bankprocessor` package.
* Add caching layer to avoid re-parsing unchanged files.
* CLI tool wraps `process_files` & `dw_director`.

---
## 8  Open Questions / Decisions Needed
1. Should `process_files` be implemented inside `dw_director` or remain a shim convenience wrapper?
2. Where should user-editable regex patterns and ML models be stored (repo vs `%APPDATA%`)?
3. Do we expose a **streaming** variant for very large files (generator)?

*Please add comments inline or raise issues in GitHub before we finalise the API.*
