# Documentation Cleanup Plan

## Current Documentation Issues

### 1. Scattered Documentation
- Multiple docs folders: `docs/`, `DOCS_module-dev/`, `z_DOCS/`, etc.
- Inconsistent naming conventions
- Duplicate information across files
- Outdated information not removed

### 2. Missing Critical Documentation
- **Padding/Layout Standards**: Just created
- **Component Architecture**: Partially documented
- **Config System**: Incomplete
- **Development Protocols**: Missing
- **Code Review Standards**: Missing

### 3. Inconsistent Structure
- No standard template for documentation
- Mixed markdown quality
- No clear hierarchy or navigation
- No index or table of contents

## Proposed Documentation Structure

```
docs/
├── _ARCHITECTURE/                 # Core system documentation
│   ├── padding_management_protocol.md     # ✅ CREATED
│   ├── qt_styling_system.md              # ✅ CREATED  
│   ├── theme_system.md                   # ✅ EXISTS
│   ├── config_system.md                  # ❌ NEEDS UPDATE
│   ├── component_architecture.md         # ❌ NEEDS CREATION
│   ├── module_system.md                  # ❌ NEEDS CREATION
│   └── database_architecture.md          # ❌ NEEDS CREATION
├── _DEVELOPMENT/                  # Development processes
│   ├── coding_standards.md               # ❌ NEEDS CREATION
│   ├── code_review_checklist.md          # ❌ NEEDS CREATION
│   ├── testing_protocols.md              # ❌ NEEDS CREATION
│   ├── git_workflow.md                   # ❌ NEEDS CREATION
│   └── debugging_guide.md                # ❌ NEEDS CREATION
├── _MODULES/                      # Module-specific documentation
│   ├── categorize/
│   │   ├── README.md                     # ❌ NEEDS CONSOLIDATION
│   │   ├── architecture.md               # ❌ NEEDS CREATION
│   │   └── user_guide.md                 # ❌ NEEDS CREATION
│   ├── update_data/
│   │   ├── README.md                     # ❌ NEEDS CREATION
│   │   └── architecture.md               # ❌ NEEDS CREATION
│   └── home/
│       └── README.md                     # ❌ NEEDS CREATION
├── _COMPONENTS/                   # Shared component documentation
│   ├── table_view_system.md              # ❌ NEEDS CONSOLIDATION
│   ├── config_system.md                  # ❌ NEEDS CREATION
│   └── base_components.md                # ❌ NEEDS CREATION
├── _USER_GUIDES/                  # End-user documentation
│   ├── getting_started.md                # ❌ NEEDS CREATION
│   ├── categorize_module.md              # ❌ NEEDS CREATION
│   └── troubleshooting.md                # ❌ NEEDS CREATION
├── _API/                          # API documentation
│   ├── core_services.md                  # ❌ NEEDS CREATION
│   ├── database_api.md                   # ❌ NEEDS CREATION
│   └── event_system.md                   # ❌ NEEDS CREATION
└── README.md                      # Main project documentation index
```

## Cleanup Protocol

### Phase 1: Consolidation (Immediate)
1. **Audit Existing Documentation**
   - List all documentation files
   - Identify duplicates and outdated content
   - Categorize by type and relevance

2. **Create Master Index**
   - Main README.md with navigation
   - Category-specific index files
   - Cross-reference related documents

3. **Archive Obsolete Documentation**
   - Move outdated docs to `z_archive/` folders
   - Mark deprecated files clearly
   - Maintain for reference but remove from main navigation

### Phase 2: Standardization (Short-term)
1. **Create Documentation Templates**
   - Architecture document template
   - Module documentation template
   - Component documentation template
   - User guide template

2. **Establish Writing Standards**
   - Consistent markdown formatting
   - Standard section headers
   - Code example formatting
   - Diagram standards

3. **Implement Review Process**
   - Documentation review checklist
   - Approval process for new docs
   - Regular review schedule

### Phase 3: Enhancement (Medium-term)
1. **Fill Critical Gaps**
   - Component architecture documentation
   - Development protocols
   - User guides

2. **Improve Navigation**
   - Table of contents generation
   - Cross-linking between documents
   - Search functionality

3. **Add Interactive Elements**
   - Code examples with syntax highlighting
   - Diagrams and flowcharts
   - Screenshots and visual guides

## Documentation Standards

### File Naming Convention
```
# Architecture documents
{system_name}_architecture.md
{system_name}_protocol.md

# Module documents  
{module_name}/README.md
{module_name}/architecture.md
{module_name}/user_guide.md

# Component documents
{component_name}_system.md
{component_name}_guide.md
```

### Document Structure Template
```markdown
# Document Title

## Overview
Brief description of what this document covers.

## Current State
What exists now (if applicable).

## Architecture/Design
Core concepts and structure.

## Implementation
How to implement/use.

## Examples
Practical examples and code snippets.

## Best Practices
Recommended approaches.

## Troubleshooting
Common issues and solutions.

## References
Links to related documentation.
```

### Code Example Standards
```markdown
# Use language-specific syntax highlighting
```python
# Python code example
def example_function():
    return "formatted code"
```

# Include file paths for context
```python
# File: src/fm/core/example.py
class ExampleClass:
    pass
```

# Show before/after for changes
```python
# Before:
layout.setContentsMargins(20, 20, 20, 20)

# After:
layout.setContentsMargins(4, 4, 4, 4)
```
```

## Immediate Actions Required

### 1. Documentation Audit
- [ ] List all existing documentation files
- [ ] Identify duplicates and conflicts
- [ ] Assess quality and relevance
- [ ] Create consolidation plan

### 2. Critical Documentation Creation
- [ ] Component architecture overview
- [ ] Development protocols document
- [ ] Code review standards
- [ ] Testing guidelines

### 3. Structure Implementation
- [ ] Create new folder structure
- [ ] Move existing docs to appropriate locations
- [ ] Create index files
- [ ] Update cross-references

### 4. Standards Implementation
- [ ] Create documentation templates
- [ ] Establish review process
- [ ] Define update schedule
- [ ] Create maintenance protocol

## Maintenance Protocol

### Regular Reviews
- **Weekly**: Check for new undocumented features
- **Monthly**: Review and update existing documentation
- **Quarterly**: Major structure and organization review

### Update Triggers
- New feature development
- Architecture changes
- Bug fixes that affect documented behavior
- User feedback on documentation

### Quality Assurance
- Peer review for all new documentation
- Regular testing of documented procedures
- User feedback collection
- Accuracy verification

## Success Metrics

### Quantitative
- Reduction in duplicate documentation
- Increase in documentation coverage
- Faster onboarding time for new developers
- Reduced support requests

### Qualitative
- Improved developer experience
- Better code consistency
- Faster problem resolution
- Higher code quality

## Conclusion

Proper documentation is essential for maintainable software. This cleanup plan provides a systematic approach to organizing, standardizing, and maintaining project documentation for long-term success.
