"""Configuration system for FlatMate application."""

import platform
from pathlib import Path
from typing import Any, Dict, Optional, Union
from enum import Enum

import yaml

from .keys import ConfigKeys, EnvMode


class SystemPaths:
    """Immutable system paths, set at startup"""
    
    def __init__(self, project_root: Optional[Path] = None):
        """Initialize system paths"""
        self.project_root = project_root or Path(__file__).parent.parent.parent.parent
        self.logs = self.project_root / 'logs'
        self.config = self.project_root / 'config'
        self.resources = self.project_root / 'resources'
        self.cache = self.project_root / 'cache'
        self.data = self.project_root / 'data'
        
        # Create system directories
        for path in [self.logs, self.config, self.resources, self.cache, self.data]:
            path.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: Union[ConfigKeys.Paths, str]) -> Path:
        """Get a system path by key"""
        if isinstance(key, str):
            key_str = key.split('.')[-1].lower()
        else:
            key_str = key.name.lower()
        return getattr(self, key_str)


class UserPaths:
    """User data paths, set at startup"""
    
    def __init__(self, user_home: Optional[Path] = None):
        """Initialize user paths"""
        self.user_home = user_home or Path('~/.flatmate').expanduser()
        self.data = self.user_home / 'data'
        self.profiles = self.user_home / 'profiles'
        self.reports = self.user_home / 'reports'
        self.master = self.data / 'master'
        self.backup = self.data / 'backup'
        self.unrecognised = self.data / 'unrecognised'  # Add unrecognised directory
        self.cache = self.user_home / 'cache'
        self.logs = self.user_home / 'logs'
        self.config = self.user_home / 'config'
        
        # Create user directories
        for path in [self.data, self.profiles, self.reports, self.master, 
                    self.backup, self.unrecognised, self.cache, self.logs, self.config]:
            path.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: Union[ConfigKeys.Paths, str]) -> Path:
        """Get a user path by key"""
        if isinstance(key, str):
            key_str = key.split('.')[-1].lower()
        else:
            key_str = key.name.lower()
        return getattr(self, key_str)


class UserPreferences:
    def _convert_enums_in_dict(self, data: Any) -> Any:
        if isinstance(data, dict):
            new_dict = {}
            for k, v in data.items():
                new_key = k.value if isinstance(k, Enum) else k
                # Ensure new_key is string if it was an Enum, otherwise YAML might still fail for non-string keys
                if not isinstance(new_key, (str, int, float, bool, type(None))):
                    # Or handle/log this case appropriately if non-basic type keys are expected and need special handling
                    new_key = str(new_key) # Fallback to string representation

                new_value = self._convert_enums_in_dict(v) # Recurse for value
                # No, new_value is already processed by recursion, direct assignment is fine.
                # The recursion handles if new_value itself was an Enum.
                new_dict[new_key] = new_value
            return new_dict
        elif isinstance(data, list):
            new_list = []
            for item in data:
                new_item = self._convert_enums_in_dict(item) # Recurse for list items
                new_list.append(new_item)
            return new_list
        elif isinstance(data, Enum):
            return data.value
        return data
    """Runtime user preferences"""
    
    def __init__(self, config_path: Path):
        """Initialize user preferences"""
        self.config_path = config_path
        self._prefs = self._load_preferences()
    
    def _load_preferences(self) -> Dict[str, Any]:
        """Load preferences from YAML file"""
        if not self.config_path.exists():
            return {}
        
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Error loading preferences: {e}")
            return {}
    
    def save(self):
        """Save preferences to YAML file"""
        try:
            with open(self.config_path, 'w') as f:
                yaml.safe_dump(self._prefs, f, default_flow_style=False)
        except Exception as e:
            print(f"Error saving preferences: {e}")
    
    def get(self, key: Union[ConfigKeys.Window, ConfigKeys.Logging, ConfigKeys.Reports, ConfigKeys.UpdateData, str], 
            default: Any = None) -> Any:
        """Get a preference value"""
        if isinstance(key, str):
            key_str = key
        else:
            key_str = key.value
        value = self._prefs.get(key_str)
        return value if value is not None else default
    
    def set(self, key: Union[ConfigKeys.Window, ConfigKeys.Logging, ConfigKeys.Reports, ConfigKeys.UpdateData, str], 
            value: Any):
        """Set a preference value, ensuring Enums are stored by their values."""
        # TODO: Review the handling of Enum keys that also subclass str.
        # This logic prioritizes Enum to ensure .value is used for str-Enums.
        if isinstance(key, Enum):  # Check for Enum FIRST
            key_str = key.value
        elif isinstance(key, str): # Then check for str
            key_str = key
        else:
            # Fallback for any other unexpected key types, convert to string
            # This case should ideally not be hit with proper Enum/str keys
            key_str = str(key) 
            # Consider logging a warning here if key is not Enum or str
            # print(f"Warning: Unexpected key type in UserPreferences.set: {type(key)}")
        
        # Recursively convert enums in the value
        # Assuming _convert_enums_in_dict is present and handles nested Enums in values
        if hasattr(self, '_convert_enums_in_dict') and callable(self._convert_enums_in_dict):
            processed_value = self._convert_enums_in_dict(value)
        elif isinstance(value, Enum): # Fallback if _convert_enums_in_dict is missing
            processed_value = value.value
        else:
            processed_value = value
        
        self._prefs[key_str] = processed_value
        self.save()


class Environment:
    """Environment detection and settings"""
    
    def __init__(self):
        """Initialize environment"""
        self.mode = EnvMode.DEV.value  # Use .value to get the string
        self.debug = False
        self.os_type = self._detect_os()
    
    def _detect_os(self) -> str:
        """Detect operating system"""
        return platform.system().lower()
    
    def get(self, key: ConfigKeys.Env) -> Any:
        """Get an environment setting"""
        return getattr(self, key.name.lower())
    
    def set(self, key: ConfigKeys.Env, value: Any):
        """Set an environment setting"""
        setattr(self, key.name.lower(), value)


class ConfigManager:
    """Central configuration management"""
    _instance = None
    
    def __new__(cls):
        """Singleton pattern"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """Initialize configuration"""
        # Add custom representer for Enum objects to save their values
        def enum_representer(dumper, data):
            return dumper.represent_scalar('tag:yaml.org,2002:str', data.value)
        yaml.SafeDumper.add_representer(Enum, enum_representer)

        self.system_paths = SystemPaths()
        self.user_paths = UserPaths()
        self.env = Environment()
        
        # Initialize preferences with YAML file
        prefs_path = self.user_paths.user_home / 'preferences.yaml'
        self.preferences = UserPreferences(prefs_path)
        
        # Load default values
        self._load_defaults()
    
    def _load_defaults(self):
        """Load default values"""
        # Load from global_defaults.yaml in core config directory
        default_config_path = Path(__file__).parent / 'global_defaults.yaml'
        if default_config_path.exists():
            try:
                with open(default_config_path, 'r') as f:
                    defaults = yaml.safe_load(f)
                    for key, value in defaults.items():
                        if self.preferences.get(key) is None:
                            self.preferences.set(key, value)
            except Exception as e:
                print(f"Error loading defaults: {e}")
    
    def get_path(self, key: Union[ConfigKeys.Paths, str]) -> Path:
        """Get a path by key"""
        # Try user paths first, then system paths
        try:
            return self.user_paths.get(key)
        except AttributeError:
            return self.system_paths.get(key)
    
    def get_env(self, key: ConfigKeys.Env) -> Any:
        """Get an environment setting"""
        return self.env.get(key)
    
    def set_env(self, key: ConfigKeys.Env, value: Any):
        """Set an environment setting"""
        self.env.set(key, value)
    
    def get_pref(self, key: Union[ConfigKeys.Window, ConfigKeys.Logging, ConfigKeys.Reports, ConfigKeys.UpdateData], 
                 default: Any = None) -> Any:
        """Get a user preference"""
        return self.preferences.get(key, default)
    
    def set_pref(self, key: Union[ConfigKeys.Window, ConfigKeys.Logging, ConfigKeys.Reports, ConfigKeys.UpdateData], 
                 value: Any):
        """Set a user preference"""
        self.preferences.set(key, value)
    
    def get_value(self, key: Any, default: Any = None) -> Any:
        """Get a value from config - delegates to get_pref"""
        return self.get_pref(key, default)
    
    def set_value(self, key: Any, value: Any):
        """Set a value in config - delegates to set_pref"""
        self.set_pref(key, value)


# Create singleton instance
config = ConfigManager()
