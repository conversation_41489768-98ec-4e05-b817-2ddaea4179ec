[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Remove index display from table DESCRIPTION:Investigate and fix the pandas DataFrame index being displayed in the table. This is likely happening in the table model or data loading process.
-[x] NAME:Add basic toolbar border styling DESCRIPTION:Add a thin 1px border around the toolbar QFrame using a very dark, muted green color. Test this single styling change to ensure it works correctly.
-[/] NAME:Add toolbar background styling DESCRIPTION:Add subtle background color to the toolbar to make it visually distinct from the table area.
-[ ] NAME:Add group separator styling DESCRIPTION:Add subtle borders between FilterGroup, ColumnGroup, and ExportGroup to visually separate the different tool sections.
-[ ] NAME:Add button styling DESCRIPTION:Style the toolbar buttons with consistent colors, borders, and hover effects that match the theme.
-[ ] NAME:Add input field styling DESCRIPTION:Style the filter input field and column dropdown with consistent appearance and focus indicators.
-[/] NAME:get documentation for pyside6/  qt system DESCRIPTION:
-[ ] NAME:remove empty time stamp from date column DESCRIPTION:
-[ ] NAME:reduce padding DESCRIPTION:reduce padding around entire FMTableView container widget (qframe?)
-[ ] NAME:Change panel butons in left panel DESCRIPTION:
- change source buttons to labeled menu (OptionMenu?) with a select button from the components / widgets folder  
- label: "Load from..." Options: ("databse", "file") Default = database.
select button: "load data"