# Refactoring Statement Handler File Reading Logic

**Date:** 2025-07-07

## 1. Problem Statement

The current implementation of the `StatementHandler` has a critical inconsistency:

- The `can_handle_file` method uses its own logic to read a file preview (`pd.read_csv(..., nrows=20)`).
- The main `process_file` method uses a separate `_read_file` method to load the full file.

These two methods **do not** share the same file-reading parameters (e.g., `header`, `skiprows`, `encoding`). This can lead to a situation where `can_handle_file` correctly identifies and approves a file, but the main processing logic fails to read it correctly, or vice-versa.

This violates the "fail fast" principle and makes debugging new handlers difficult.

## 2. Proposed Solution

To resolve this, I propose refactoring the file-reading logic into a single, centralized internal method within `_base_statement_handler.py`. This ensures both the preview and the full read operations use the exact same, handler-specific configuration.

### Step-by-Step Plan:

1.  **Create an intelligent, private `_read_csv` helper method** in `StatementHandler`.
    -   This method will be the single source of truth for reading CSV files.
    -   It will accept a `filepath` and an optional `nrows` argument.
    -   **The method will replicate the existing, conditional header logic.** It will inspect the handler's configuration:
        -   If `ColumnAttributes.colnames_in_header` is `True`, it will read the CSV using `header=ColumnAttributes.col_names_row`.
        -   If `False`, it will read the CSV using `header=None`.
    -   **No rows will be skipped** during this initial read, ensuring all data is loaded for subsequent processing.

2.  **Update `can_handle_file`**
    -   This method will use the `_read_csv` helper to get a preview of the file.
    -   The rest of its existing validation logic (checking filename, headers, account numbers, etc.) will remain the same, but will now operate on a consistently-read DataFrame preview.

3.  **Enhance `format_df`**
    -   This becomes the core of the logic.
    -   It will receive the DataFrame from `_read_csv`.
    -   **Step 1: Extract Metadata.** It will first extract the account number and any other metadata from the DataFrame, based on the handler's configuration.
    -   **Step 2: Clean DataFrame.** Only after extraction, it will clean the DataFrame by removing the metadata rows and setting the correct column headers.
    -   **Step 3: Format Data.** It will then proceed with formatting the remaining transaction data as before.

## 3. Rationale & Benefits

-   **Consistency & Reliability**: Guarantees that the file preview used for validation is read in the exact same way as the full file used for processing. This eliminates a whole class of potential bugs.
-   **Maintainability**: Centralizes the `pd.read_csv` call into one location. If we need to add new reading options in the future, we only need to update one method.
-   **Adherence to Design Principles**: This change reinforces the principle that the handler is the ultimate authority on its file format.

## 4. Impact

-   This is a targeted, internal refactoring of `_base_statement_handler.py`.
-   The public interface of the handlers (`process_file`) will not change.
-   The impact on concrete handlers is minimal. No changes are required as this refactoring uses the existing attributes.

This surgical change will make the statement handling system more robust and easier to maintain without a major rewrite.
