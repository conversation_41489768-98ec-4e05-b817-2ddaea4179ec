app:
  debug_mode: true
  environment: development
  is_first_run: false
  name: flatMate
  version: 1.0.0
categorize.database.auto_load_on_startup: true
categorize.database.default_load_from_db: true
categorize.database.default_sort_column: amount
categorize.database.default_sort_order: ascending
categorize.database.last_account: test_account_123
categorize.database.last_end_date: '2025-07-15'
categorize.database.last_start_date: '2025-06-15'
categorize.database.remember_last_query: true
categorize.filters.default_days_back: 30
categorize.filters.use_default_date_range: true
files:
  config_extensions:
  - .json
  - .yaml
  - .yml
  data_extensions:
  - .csv
  - .xlsx
  - .json
  image_extensions:
  - .png
  - .jpg
  - .jpeg
  max_size: ********
  supported_extensions:
  - .csv
  - .xlsx
  - .json
  - .yaml
  - .yml
logging:
  backup_count: 5
  console_level: INFO
  file_path: ~/.flatmate/logs/app.log
  level: DEBUG
  max_file_size_mb: 10
  show_info: true
  show_warnings: true
paths:
  backup_dir: ~/.flatmate/backups
  base_data_dir: ~/.flatmate
  cache: ~/.flatmate/cache
  config: ~/.flatmate/config
  data: ~/.flatmate/data
  logs: ~/.flatmate/logs
  master: ''
  master_history: []
  recent_files:
  - home
  - update_data
  recent_masters: []
  temp_dir: ~/.flatmate/temp
profiles:
  active_profile: default
  default: {}
security:
  data_anonymization: false
  encryption_enabled: true
testing:
  mock_data_enabled: false
  test_mode: false
