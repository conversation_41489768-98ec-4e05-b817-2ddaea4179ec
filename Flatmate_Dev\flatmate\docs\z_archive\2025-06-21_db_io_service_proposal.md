# Proposal: Unified Database I/O Service Layer

## 1. Context & Motivation
The Flatmate application currently accesses persistence through the `database_service` package.  The CLI tool has begun to diverge by introducing direct repository calls.  To keep a single source-of-truth *and* expose richer convenience operations (import / export / stats, etc.) we should introduce a thin, high-level `db_io_service` that wraps the low-level repositories while re-using the existing `DataService` abstractions.

## 2. Goals
1. **Consistency** – All entry-points (GUI, CLI, tests) hit the same public API.
2. **Separation of Concerns** – Core SQL logic lives in one place and changes rarely.
3. **Discoverability** – Clear folder & module names make dependencies obvious.
4. **Extensibility** – Easy to add future I/O formats (Parquet, JSON, etc.).
5. **Safety** – No breaking changes to the rest of the app during migration.

## 3. Current Structure (simplified)
```
fm/
  database_service/
    repository/
      sqlite_repository.py
      transaction_repository.py
    service.py          # DataService
```

*Pain points*
- `DataService` mixes read/write helpers with GUI-specific adapters.
- CLI bypasses `DataService` and reaches directly to `SQLiteTransactionRepository`.
- Difficult to add new formats without touching multiple layers.

## 4. Proposed Structure
```
fm/
  core/
    database/                   # <<< rarely touched core DB layer
      db_io_service.py         # NEW high-level convenience API
      sql_repository/
        sqlite_repository.py    # unchanged
        transaction_repository.py
      models/                   # (optional) dataclasses / pydantic models

  services/
    data_service.py             # (kept for backward compatibility – thin wrapper)

cli/
  db_commands.py                # uses db_io_service instead of repos
```
### Naming Rationale
| Layer              | Responsibility                              |
|--------------------|---------------------------------------------|
| `database`         | Pure DB access, SQL & ORM, no business logic|
| `sql_repository`   | CRUD operations on individual aggregates    |
| `services`         | Application-facing orchestration helpers    |
| `db_io_service.py` | Import/export, stats, bulk helpers          |

Alternative names considered:
- `storage` – shorter, but less explicit than *database*.
- `persistence` – common in other frameworks but less direct than *database*.
- `infra/db` – reflects infrastructure, but deeper nesting.

## 5. `db_io_service` – API Sketch
```python
class DBIOService:
    def __init__(self, repo: SQLiteTransactionRepository | None = None): ...

    # --- Importing --------------------------------------------------
    def import_csv(self, file_path: Path, mapping: dict[str, str] | None = None) -> ImportResult: ...
    def import_dataframe(self, df: pd.DataFrame) -> ImportResult: ...

    # --- Exporting --------------------------------------------------
    def export_dataframe(self, *, all_cols: bool = False) -> pd.DataFrame: ...
    def export_csv(self, target: Path, *, all_cols: bool = False, **to_csv_kwargs) -> None: ...

    # --- Query helpers ---------------------------------------------
    def list_transactions(self, **filters) -> list[Transaction]: ...
    def stats(self) -> dict[str, Any]: ...
```
*Notes*
- Accepts a repository instance for easier unit-testing.
- Uses pandas for tabular conveniences.
- Keeps `DataService` as thin delegate for existing callers until migrated.

## 6. Pros & Cons
| Option | Pros | Cons |
|--------|------|------|
| **Keep status-quo** | No code churn | Fragmented logic, harder to add features |
| **Introduce `db_io_service` (proposed)** | Single high-level API, small risk, keeps repos untouched | Needs light refactor of CLI & callers |
| **Full rewrite / new ORM** | Clean slate, modern patterns | Very high effort & risk |

## 7. Migration Plan (Incremental)
1. **Create package/folders** as above; copy existing `database_service` into `fm.core.database` with repositories in `sql_repository/`.
2. **Implement `db_io_service.py`** with methods wrapping the existing repositories.
3. **Refactor CLI** to use `db_io_service` (small surface).
4. **Add unit tests** for new service.
5. **Gradually migrate GUI/data-layer calls** from `DataService` to `DBIOService`.
6. Once stable, **deprecate** direct repository usage & trim redundant helpers.

## 8. Recommendations
- Proceed with *Option 2* (introduce `db_io_service`).
- Keep `DataService` as façade during transition to avoid widespread breakage.
- Adopt the `core.database` / `services` naming scheme for clarity.
- Use type hints and docstrings aggressively; this layer will be the public API.
- Add `--all-cols` export in CLI to showcase new service flexibility.

## 9. Open Questions
1. Should we keep `database_service` name for backward compatibility and alias it to `core.database`?  
2. Do we need repository interfaces (abstract base classes) for easier mocking?  
3. Preferred date/time handling library (native vs. `pendulum`)?

---
*Updated 2025-06-21 by Cascade AI assistant*
