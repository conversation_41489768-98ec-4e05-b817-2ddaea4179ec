#!/usr/bin/env python3
"""Test the new config helper methods in BaseLocalConfigV2.

This demonstrates:
1. list_available_keys() - discover what can be configured
2. set_user_preference() - override defaults
3. get_user_preference() - check current overrides
4. reset_to_default() - restore original values
5. export_user_preferences() - see current customizations
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

def test_config_helpers():
    """Test the new config helper methods."""
    print("🧪 Testing Config Helper Methods")
    print("=" * 50)
    
    try:
        from fm.modules.categorize.config import config
        print("✅ Successfully imported categorize config")
        
        # First, create some config through normal usage
        print("\n🏗️  Creating config through normal usage...")
        config.ensure_defaults({
            'categorize.display.table_margin': 2,
            'categorize.display.row_height': 25,
            'categorize.filters.default_days_back': 30,
            'categorize.columns.max_width': 40,
        })
        print("✅ Config created through ensure_defaults()")
        
        # Test 1: list_available_keys()
        print("\n📋 Test 1: list_available_keys()")
        available = config.list_available_keys()
        print(f"Available config keys ({len(available)}):")
        for key, value in available.items():
            print(f"  • {key}: {value}")
        
        # Test 2: set_user_preference()
        print("\n🎯 Test 2: set_user_preference()")
        print("Setting user preference: table_margin = 5")
        config.set_user_preference('categorize.display.table_margin', 5)
        
        # Verify the change took effect
        new_value = config.get_value('categorize.display.table_margin')
        print(f"✅ New value: {new_value}")
        
        # Test 3: get_user_preference()
        print("\n🔍 Test 3: get_user_preference()")
        user_pref = config.get_user_preference('categorize.display.table_margin')
        print(f"User preference for table_margin: {user_pref}")
        
        no_pref = config.get_user_preference('categorize.display.row_height')
        print(f"User preference for row_height (should be None): {no_pref}")
        
        # Test 4: Set another preference
        print("\n🎯 Setting another user preference...")
        config.set_user_preference('categorize.filters.default_days_back', 60)
        print("Set default_days_back = 60")
        
        # Test 5: export_user_preferences()
        print("\n📄 Test 5: export_user_preferences()")
        user_prefs_yaml = config.export_user_preferences()
        print("Current user preferences as YAML:")
        print(user_prefs_yaml)
        
        # Test 6: reset_to_default()
        print("\n🔄 Test 6: reset_to_default()")
        print("Resetting table_margin to default...")
        config.reset_to_default('categorize.display.table_margin')
        
        reset_value = config.get_value('categorize.display.table_margin')
        print(f"✅ Value after reset: {reset_value}")
        
        # Test 7: Show final state
        print("\n📊 Final State:")
        final_available = config.list_available_keys()
        print("All available keys:")
        for key, value in final_available.items():
            user_override = config.get_user_preference(key)
            if user_override is not None:
                print(f"  • {key}: {value} (USER OVERRIDE: {user_override})")
            else:
                print(f"  • {key}: {value} (default)")
        
        # Test 8: Error handling
        print("\n❌ Test 8: Error handling")
        try:
            config.set_user_preference('categorize.nonexistent.key', 'value')
            print("ERROR: Should have failed!")
        except ValueError as e:
            print(f"✅ Correctly caught error: {e}")
        
        print("\n✅ All helper method tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Helper method tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_workflow():
    """Demonstrate the typical user workflow."""
    print("\n" + "=" * 50)
    print("🎯 Demonstrating Typical User Workflow")
    print("=" * 50)
    
    try:
        from fm.modules.categorize.config import config
        
        print("1. 📋 User discovers available settings:")
        available = config.list_available_keys()
        print(f"   Found {len(available)} configurable settings")
        
        print("\n2. 🎯 User customizes some settings:")
        config.set_user_preference('categorize.display.row_height', 30)
        config.set_user_preference('categorize.columns.max_width', 50)
        print("   Customized row_height and max_width")
        
        print("\n3. 📄 User exports their preferences:")
        prefs = config.export_user_preferences()
        print("   User preferences YAML:")
        for line in prefs.split('\n')[:10]:  # Show first 10 lines
            print(f"   {line}")
        
        print("\n4. 🔄 User resets one setting:")
        config.reset_to_default('categorize.display.row_height')
        print("   Reset row_height to default")
        
        print("\n✅ Workflow demonstration complete!")
        return True
        
    except Exception as e:
        print(f"\n❌ Workflow demonstration failed: {e}")
        return False

if __name__ == "__main__":
    success1 = test_config_helpers()
    success2 = demonstrate_workflow()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 Config helper methods working perfectly!")
        print("💡 Users can now easily discover and customize settings")
        print("🔧 Ready for Sprint 1.3: User Preferences Persistence")
    else:
        print("🔧 Issues found - need to investigate further")
    
    sys.exit(0 if (success1 and success2) else 1)
