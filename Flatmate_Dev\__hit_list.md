# Flatmate Development Hit List

## High Priority
- [ ] Fix database loading issue
  - Investigate and resolve why the database isn't loading
  - Check connection strings and database file permissions
  - Verify database initialization on startup

## Medium Priority
- [ ] Left Panel Collapse/Expand Functionality
  - Add collapse/expand icon to left panel
  - Implement show/hide methods for the panel
  - Integrate with category view

- [ ] Filtering System Redesign
  - Evaluate current filtering implementation
  - Consider loading all transactions by default
  - Implement last used settings persistence
  - Improve filter UI/UX in left panel
  - Document design decisions and implementation plan
  >> *filter by date should be collpsible or a drop down menu* - the question is should we load all transactions and filter after, or filter first....

## Implementation Notes
- All changes should maintain current functionality
- Follow existing code style and patterns
- Add logging for debugging
- Consider performance implications for large transaction sets

## high priority, application should open to last used module 
- and last used window state and size 
- restored down state should be a sensible default - it would be nice if it didint open bigger than the window its in .. alot of apps do this in a multi screen set up 

# cat_data  
should be renamed to this, currently categorise 
## currnent issues:
default should be to open from db  with last loaded db_query and and any filters appiled
default visible columns applied with default widths applied and details taking up any available space 
(logic should already be there somewhere)
default order should be applied this is an issue across the app
it used to be set by fm standard columns enum
the tableview columns drop down should use the same ordering logic 
The order should be set by how likely to be used 
The collumn display order should be remembered 
collumns for categorise df should be set either in groups or a method
should remember last selected columns for display which should over ride defaults 
columns should remember last set widths and order 
column autosizing seems disabled details coulumn should expand to take up available space up to a senisble max then notes if displayed 

 
