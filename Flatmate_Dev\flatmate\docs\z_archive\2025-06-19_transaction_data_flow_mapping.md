# Transaction Data Flow Mapping

## Current System Architecture

### Data Sources → Database → UI Flow

```
Bank Statements → UD Pipeline → Database → Categorize Module → UI
     ↓              ↓            ↓           ↓               ↓
  CSV Files    StandardColumns  db_names   DataFrame    Display Names
```

## Key Constraints & Requirements

### 1. **fm_standard_columns is CANONICAL**
- UD Pipeline uses StandardColumns to populate database
- Database stores data using `db_name` format (lowercase, underscores)
- UI needs `display_name` format (proper case, spaces)

### 2. **Database Schema Reality**
- Database contains MORE columns than just StandardColumns
- Additional columns: `category`, `notes`, `tags`, `source_bank`, etc.
- System must handle both known (StandardColumns) and unknown columns

### 3. **Column Handling Rules**
- **Known columns** (in StandardColumns): Use canonical display names
- **Unknown columns** (not in StandardColumns): Show as-is, unmodified, shunted to end
- **All columns** must be available in UI column visibility menu

## Current Data Flow Detailed

### Phase 1: Bank Statements → Database
```
1. Bank CSV Files
   ├─ Various formats (different banks)
   ├─ Different column names
   └─ Raw transaction data

2. UD Pipeline (update_data module)
   ├─ Uses StandardColumns for mapping
   ├─ Converts display names → db_names
   ├─ Standardizes data format
   └─ Handles bank-specific variations

3. Database Storage
   ├─ Stores using db_names (date, details, amount, etc.)
   ├─ Additional columns added over time
   ├─ Schema defined by StandardColumns + extras
   └─ Source of truth for available columns
```

### Phase 2: Database → Categorize Module
```
1. Database Query
   ├─ Returns ALL columns in database
   ├─ Uses db_names format
   └─ Includes both standard and custom columns

2. cat_presenter.py (BROKEN - needs column manager)
   ├─ Converts Transaction objects → DataFrame
   ├─ Adds missing columns (category, tags, notes)
   ├─ Needs column name mapping for UI
   └─ Currently broken due to missing get_column_manager()

3. UI Display
   ├─ Needs display_names for user-friendly headers
   ├─ Column visibility menu needs ALL available columns
   ├─ Export functions need proper column names
   └─ Users don't want to see "code_friendly_names"
```

## The Problem We Created

### What We Had (Working)
```python
# Simple, working approach:
df = pd.DataFrame([transaction.__dict__ for transaction in transactions])
column_mapping = StandardColumns.get_db_column_mapping()
# Display names from StandardColumns, fallback for unknown columns
```

### What We Built (Over-engineered)
```python
# Complex, broken approach:
column_manager = get_column_manager()  # Elaborate system
df = column_manager.convert_transactions_to_dataframe(transactions)
# Multiple layers of abstraction, preferences, migration utils, etc.
```

## Simple Solution Requirements

### Core Functions Needed
1. **`get_all_available_columns()`** - From database schema
2. **`get_column_display_mapping()`** - db_name → display_name using StandardColumns
3. **`prepare_dataframe_for_ui()`** - Add missing columns, apply mapping

### Implementation Strategy
```python
def get_all_available_columns() -> List[str]:
    """Get all columns from database schema."""
    # Use PRAGMA table_info(transactions)
    
def get_column_display_mapping() -> Dict[str, str]:
    """Map db_names to display_names using StandardColumns."""
    # Use StandardColumns.get_db_column_mapping() 
    # Fallback for unknown columns: db_name.replace('_', ' ').title()
    
def prepare_dataframe_for_ui(df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, str]]:
    """Prepare DataFrame with all columns and display mapping."""
    # Ensure all database columns exist in DataFrame
    # Return DataFrame + column mapping for UI
```

## Next Steps

1. **Create Mermaid diagram** of complete data flow
2. **Implement simple column utilities** (3 functions max)
3. **Fix cat_presenter.py** to use simple approach
4. **Test with actual database** to verify all columns appear
5. **Ensure column ordering**: StandardColumns first, unknown columns at end

## Key Principle

**Keep it simple**: The system just needs to bridge between database storage (db_names) and UI display (display_names) using StandardColumns as the canonical mapping source.
