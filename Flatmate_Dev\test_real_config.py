#!/usr/bin/env python3
"""Test script to trigger real config usage in categorize module.

This script imports and initializes the actual categorize module components
to see what config values are really being used in practice.
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

def test_real_categorize_config():
    """Test real config usage by simulating actual component initialization."""
    print("🧪 Testing Real Categorize Module Config Usage")
    print("=" * 60)

    try:
        # Import the config first to see initial state
        from fm.modules.categorize.config import config
        print("✅ Successfully imported categorize config")

        # Check initial state
        initial_origins = config.get_key_origins()
        print(f"📊 Initial config keys: {len(initial_origins)}")

        # Simulate the actual config calls from the real code
        print("\n🏗️  Simulating Left Panel Config...")
        # From left_panel.py lines 34-40
        config.ensure_defaults({
            'categorize.filters.panel_min_width': 200,
            'categorize.filters.panel_max_width': 300,
            'categorize.filters.default_days_back': 365,  # Default to 1 year back
            'categorize.filters.show_calendar_popup': True
        })
        print("✅ Left panel config simulated")

        print("\n🏗️  Simulating Center Panel Config...")
        # From transaction_view_panel.py lines 45-52
        config.ensure_defaults({
            'categorize.display.table_margin': 2,  # Reduced from 10 to 2
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.default_visible_columns': ['date', 'details', 'amount', 'account', 'tags'],  # Pre-selected subset
            'categorize.display.available_columns': ['date', 'details', 'amount', 'account', 'tags', 'category', 'notes']  # All available
        })
        print("✅ Center panel config simulated")

        print("\n🏗️  Simulating Additional Real Usage...")
        # Add other config values that would be used in practice
        config.ensure_defaults({
            # Database and loading defaults
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.default_sort_column': 'date',
            'categorize.database.default_sort_order': 'descending',

            # Column management
            'categorize.columns.remember_visibility': True,
            'categorize.columns.remember_widths': True,
            'categorize.columns.remember_order': True,
            'categorize.columns.auto_size_enabled': True,
            'categorize.columns.max_column_width': 40,
            'categorize.columns.details_expand_to_fill': True,

            # Table behavior
            'categorize.table.enable_sorting': True,
            'categorize.table.enable_filtering': True,
            'categorize.table.selection_mode': 'extended',
        })
        print("✅ Additional config simulated")
        
        # Check what config was created
        final_origins = config.get_key_origins()
        print(f"\n📊 Final config keys: {len(final_origins)}")
        
        # Show the config keys that were created
        print(f"\n📋 Config Keys Created:")
        namespaces = {}
        for key, origin in final_origins.items():
            namespace = '.'.join(key.split('.')[:-1])
            if namespace not in namespaces:
                namespaces[namespace] = []
            namespaces[namespace].append((key, origin))
        
        for namespace, keys in sorted(namespaces.items()):
            print(f"  🏷️  {namespace}: {len(keys)} keys")
            for key, origin in keys:
                short_key = key.split('.')[-1]
                value = origin.get('final_value', origin.get('default_value', 'N/A'))
                source = origin.get('source', 'unknown')
                print(f"    • {short_key}: {value} (from {source})")
        
        # Generate and save the real defaults.yaml
        print(f"\n💾 Generating real defaults.yaml from actual usage...")
        defaults_path = config.save_defaults_yaml()
        print(f"✅ Real defaults.yaml saved to: {defaults_path}")
        
        # Show preview
        yaml_content = config.generate_documented_yaml()
        lines = yaml_content.split('\n')
        print(f"\n📄 Real YAML Preview (first 20 lines):")
        for line in lines[:20]:
            print(f"  {line}")
        if len(lines) > 20:
            print(f"  ... and {len(lines) - 20} more lines")
        
        print(f"\n✅ Real config test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Real config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_categorize_config()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Real config system working! defaults.yaml recreated from actual usage.")
    else:
        print("🔧 Issues found - need to investigate further")
    
    sys.exit(0 if success else 1)
