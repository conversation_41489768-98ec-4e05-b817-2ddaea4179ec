#!/usr/bin/env python3
"""
Test script to verify the improved validation logic works correctly.
"""
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime

# Add the src directory to the path
src_dir = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_dir))

def test_validation_logic():
    """Test the improved validation logic with realistic banking scenarios."""
    print("Testing improved validation logic...")
    
    try:
        from fm.core.data_services.standards.columns import Columns
        from fm.modules.update_data.utils.dw_pipeline import _find_suspicious_transactions
        
        # Create test data with various banking scenarios
        test_data = [
            # Normal transactions - should NOT be flagged
            {'date': '2024-01-01', 'amount': -100.0, 'balance': 900.0, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'Normal purchase'},
            {'date': '2024-01-02', 'amount': 50.0, 'balance': 950.0, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'Normal deposit'},
            
            # Dishonored payment - should NOT be flagged (amount=0, balance unchanged)
            {'date': '2024-01-03', 'amount': 0.0, 'balance': 950.0, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'AP DISHONOUR #12345'},
            
            # Insufficient funds - should NOT be flagged (amount=0, balance unchanged)
            {'date': '2024-01-04', 'amount': 0.0, 'balance': 950.0, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'POSREJ Insuff. Funds-14:57'},
            
            # Small interest charge - should NOT be flagged (legitimate small amount)
            {'date': '2024-01-05', 'amount': -0.01, 'balance': 949.99, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'INTEREST DEBIT'},
            
            # Real error - should BE flagged (balance doesn't match calculation)
            {'date': '2024-01-06', 'amount': -50.0, 'balance': 949.99, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'Purchase with wrong balance'},
            
            # Another real error - should BE flagged (significant balance mismatch)
            {'date': '2024-01-07', 'amount': 100.0, 'balance': 950.0, 'account': 'ACC1', 'source_file': 'file1.csv', 'details': 'Deposit with wrong balance'},
        ]
        
        # Create DataFrame
        df = pd.DataFrame(test_data)
        df.columns = [Columns.DATE.db_name, Columns.AMOUNT.db_name, Columns.BALANCE.db_name, 
                     Columns.ACCOUNT.db_name, Columns.SOURCE_FILENAME.db_name, Columns.DETAILS.db_name]
        
        # Convert date column
        df[Columns.DATE.db_name] = pd.to_datetime(df[Columns.DATE.db_name])
        
        print(f"Created test DataFrame with {len(df)} transactions")
        print("\nTest data:")
        for i, row in df.iterrows():
            print(f"  Row {i+1}: Amount={row[Columns.AMOUNT.db_name]}, Balance={row[Columns.BALANCE.db_name]}, Details={row[Columns.DETAILS.db_name][:30]}...")
        
        # Run validation
        suspicious = _find_suspicious_transactions(df)
        
        print(f"\nValidation results:")
        print(f"Found {len(suspicious)} suspicious transactions")
        
        if len(suspicious) > 0:
            print("\nSuspicious transactions:")
            for idx, row in suspicious.iterrows():
                print(f"  Row {idx+1}: Amount={row[Columns.AMOUNT.db_name]}, Balance={row[Columns.BALANCE.db_name]}, Details={row[Columns.DETAILS.db_name][:30]}...")
        
        # Expected: Should only flag rows 6 and 7 (the real errors)
        expected_suspicious_rows = [5, 6]  # 0-based indices
        actual_suspicious_rows = list(suspicious.index)
        
        print(f"\nExpected suspicious rows: {[r+1 for r in expected_suspicious_rows]} (1-based)")
        print(f"Actual suspicious rows: {[r+1 for r in actual_suspicious_rows]} (1-based)")
        
        if set(actual_suspicious_rows) == set(expected_suspicious_rows):
            print("✓ Validation logic working correctly!")
            return True
        else:
            print("✗ Validation logic needs adjustment")
            return False
            
    except Exception as e:
        print(f"✗ Error testing validation logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    print("=" * 60)
    print("Testing Improved Validation Logic")
    print("=" * 60)
    
    success = test_validation_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ Test passed! The validation logic should now produce fewer false positives.")
        print("✓ It will only flag transactions with actual balance calculation errors.")
        print("✓ Legitimate banking scenarios (dishonors, insufficient funds, etc.) are ignored.")
    else:
        print("✗ Test failed. The validation logic may need further adjustment.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
