# Kiwibank Handler Issue Analysis

## 1. Current State

### File Loading
- Recent changes modified CSV loading to use `header=None` by default
- This breaks the Kiwibank handler which expects headers
- File loading is handled in `file_utils.py`

### Handler System Analysis

#### Base Handler (`_base_statement_handler.py`)
- Implements `matches_statement_type()`
- Provides comprehensive validation (column counts, headers, account numbers)
- Used as parent class by all handlers

#### Kiwibank Full CSV Handler (`kiwibank_full_csv_handler.py`)
- Implements `can_handle_file()` (different from base class)
- Last modified long time ago (appears to be oldest implementation)
- Only handler not using standard method name

#### Other Handlers (Kiwibank Basic, ASB, Co-op)
- All rely on base class's `matches_statement_type()`
- No custom matching methods implemented
- Only implement specific behavior when needed (e.g., `_extract_account_number`)

### Registry Behavior
- Expects `matches_statement_type()` method
- Skips handlers that don't implement this exact method name
- This explains why Kiwibank handler is being ignored

## 2. Root Cause Analysis

The Kiwibank handler stopped working due to a combination of factors:

1. **Method Name Mismatch**
   - Handler implements `can_handle_file()` but registry looks for `matches_statement_type()`
   - This is the only handler with this inconsistency
   - All other handlers correctly use the base class implementation

2. **Header Handling Changes**
   - Recent changes to file loading set `header=None` by default
   - Kiwibank handler expects headers to be present
   - Other handlers are more flexible with header presence

3. **Historical Context**
   - Appears to be the oldest handler implementation
   - May predate standardization on `matches_statement_type`
   - Other handlers were implemented later following the established pattern

## 3. Impact Assessment

### Affected Components
- `file_utils.py`: File loading logic
- `kiwibank_full_csv_handler.py`: Kiwibank statement handling
- `_handler_registry.py`: Handler selection logic

### Risk Level: Low to Medium
- **Low Risk**:
  - Only one handler needs modification
  - Change is minimal (method rename + call to parent)
  - No changes to core logic required
- **Medium Risk**:
  - Changes needed in critical file handling code
  - Requires thorough testing of all statement formats
  - Potential impact on other handlers

### Testing Coverage
- **Must Test**:
  - Kiwibank full CSV files (with and without headers)
  - ASB statement files (which triggered the original change)
  - Other handlers to ensure no regression
- **Test Cases**:
  - Files with headers in first row
  - Files with headers in metadata
  - Files with no headers (fallback case)

## 4. Recommended Solution

### File Loading
1. Load files with headers by default
2. Fall back to headerless loading only if needed
3. Preserve file metadata (like `has_headers` flag)

### Handler Update
1. Update Kiwibank handler to use `matches_statement_type`
2. Maintain existing validation logic
3. Call parent's implementation for common checks
4. Ensure backward compatibility

## 5. Implementation Plan

1. **Phase 1: File Loading**
   - Revert to header-first loading
   - Add fallback for headerless files
   - Add proper metadata

2. **Phase 2: Handler Updates**
   - Update method names to match registry expectations
   - Preserve existing validation logic
   - Add comprehensive logging

3. **Phase 3: Testing**
   - Test with Kiwibank statements
   - Test with ASB statements
   - Verify no regression in other handlers

## 6. Next Steps

1. Review and approve the proposed changes
2. Implement changes incrementally
3. Test thoroughly after each change
4. Document any new behaviors or requirements
