# Categorise Module – UI/UX Ergonomics & Widget Options

_Last updated: 2025-06-16_

## 1 Current Table Limitations
* Raw `QTableView` requires multiple clicks to edit categories.
* No quick search/filter; scrolling a long list can be slow.
* Cannot group/summarise transactions for high-level view.
* Switching modules reloads data, losing unsaved edits (caching needed).

## 2 Recommended Enhancements
### 2.1 Category Editing Improvements
| Option | Effort | Pros | Cons |
|--------|--------|------|------|
| Delegate with `QComboBox` per “category” cell | Low | Familiar dropdown, keyboard navigation | Still one row at a time |
| Sidebar batch editor (select rows → set category) | Medium | Fast bulk edits | Extra UI component |
| Inline auto-complete (`QCompleter`) | Low | Quick typing, fewer mouse clicks | Slightly more code |

_Start with delegate dropdown; later layer batch-edit._

### 2.2 Better Viewing Widgets
| Widget | Library | Notes |
|--------|---------|-------|
| `QTableView` (keep) + header filters (`QSortFilterProxyModel`) | Qt builtin | Add per-column search boxes. |
| `QTreeView` with grouping by month/category | Qt builtin | Visual aggregation; needs custom model. |
| `qtpandas` DataFrameModel viewer | Third-party | Excel-like; heavier dep. |
| `datatable` or `polars` table widget | Third-party | Not mature for Qt yet. |

_Recommendation: stick with `QTableView` + `QSortFilterProxyModel` for MVP, reassess later._

### 2.3 Search & Filter
1. Global search box filtering description/notes.
2. Per-column quick filter row (uses proxy model regex).
3. Toggle to show only *uncategorised* rows for fast triage.

### 2.4 Split-View Option
Two-pane: table left, details/receipt preview right (future enhancement).

## 3 In-Session Caching Strategy
* Presenter keeps `self._current_df` with edited values.
* On navigation away, `ModuleCoordinator` stores that DataFrame in an in-memory cache (dict keyed by module).
* When returning, the presenter checks cache first before re-query/loading.
* Persist unsaved edits: add a “dirty” flag and prompt on exit.

_Minimal implementation: keep DataFrame inside presenter instance; ensure `cleanup()` is **not** called on simple tab switches._

## 4 Open Questions
1. Should edits auto-save to DB or require explicit “Save Categories”?  
2. Do we need undo/redo support for category changes?  
3. Accept third-party lib (e.g. `qtpandas`) or avoid to keep bundle small?

---
_Comments welcome – tick items in `current_tasks.md` as decisions are made and features implemented._



DEV_NOTES: definately keen to have the looking a bit more modern 
not sure about getting module coordinator involved in cacheing ... perhaps create a caching service in core 
and have the modules manage their own caching .. ( on exit) 
with texception of start up in which any open views would be saved to temp file ... and perhaps any cached views if not accessed in long time .. especialy if large..

qtpandas hasnt been maintained in nearly ten years so .. thats probably not ideal

Dates hould be sorted in order - columns need to be resizeable sortable and filterable and we will need an option to hide columns 
for the most part we will only need to show the "required columns" with an option to show all ..


for categorisation - we're going to need some options, such filtering terms , and these will need to be saved we are going to need a utility column - with with a button or drop down menu in each cell this gets to the heart of the categorisation functionality .. and this is probably worth a document all its own ...



also Im wondering about layout and whether we should have a top based menu because sprad sheets need that horizontal real estate.. but thats a lot to do and might need to go to a future revision 

but we certainly need to improve this table view widget ..

---

## 5  Developer Notes (2025-06-16)
These points capture the latest discussion and will guide next design iterations.

### 5.1 Modern Look & Layout
* Refresh Qt stylesheet for a cleaner, flat appearance.
* Consider moving primary controls to a **top menu bar** to free horizontal space for the table (deferred to v2).

### 5.2 Categorisation Utility Column
* Add a "⚙" column with per-row dropdown / button offering:
  * Quick-set category (recent / favourites).
  * Add description pattern to category rules.
  * Mark row as reviewed / ignore.
* Bulk actions: if multiple rows selected, same dropdown applies to all.
* Persist filter terms and favourites in user config.

### 5.3 Caching Service
* New `fm.core.cache` module:
  * `CacheService.put(key, obj)` / `get(key)` / `invalidate(key)`.
  * In-memory store with optional spill to temp-file (pickle) for large DataFrames.
  * Presenters save edited DataFrames on `cleanup()`; restore on `initialize()`.
  * Expiry policy (time or LRU) to avoid unbounded memory.

### 5.4 Table Feature Requirements
* Date column sorted descending by default.
* Columns: sortable, resizable, filterable via `QSortFilterProxyModel`.
* Column visibility toggle – only required columns shown initially.
* Ability to hide/show all optional columns quickly.

### 5.5 Database Query & Data Integrity
* **Scoped queries**: extend `DataService.get_transactions()` to accept `start_date`, `end_date`, and `account_id` params so Categorise can load a targeted subset.
* **UI controls**: date‐range picker and account dropdown above the table; feeding those params into the presenter’s DB fetch.
* **Canonical columns read-only**: ID, date, amount, description, etc. locked in the model (non-editable flags).
* **Tags column**: editable text, comma-separated; multiple words allowed (e.g., `food, shared expense`).  Stored in new `tags` column in DB.
* **Validation**: presenter trims spaces and enforces no modification to canonical columns before saving.

---
