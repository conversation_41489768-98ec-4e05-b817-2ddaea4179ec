# Table View System Column Requirements

*Date: 2025-06-27*

This document details how the shared table view system uses the column system, focusing on column mapping, display, and specific requirements. This information is critical for the column system unification project to ensure all existing functionality is preserved.

## Overview

The table view system (`fm_table_view.py` and related components) provides a reusable UI component for displaying tabular data across the application. It uses the `ColumnNameService` for mapping between database and display names, and relies on the canonical `StandardColumns` enum from `fm_standard_columns.py` as the source of truth for column names.

## Key Components

1. **CustomTableView_v2** (`fm_table_view.py`)
   - Main entry point for the enhanced table system
   - Provides a complete table widget with filtering, column management, and export
   - Uses `TableConfig` for configuration and `ColumnNameService` for column mapping

2. **TableViewCore** (`components/table_view_core.py`)
   - Core table implementation with advanced features
   - Handles column visibility, sizing, and data management
   - Uses `EnhancedTableModel` for data storage

3. **TableConfig** (`components/table_config_v2.py`)
   - Configuration class for table behavior and appearance
   - Stores column widths, visibility settings, and display mappings
   - Provides convenience methods for module-specific defaults

4. **ColumnNameService** (SUPERSEDED)
   - Provides mapping between database and display names
   - Uses `StandardColumns` as the canonical source of column definitions
   - Note: Marked as superseded by `ColumnManager`, but still used by the table view system

## Column Mapping and Display

The table view system uses a sophisticated approach to column mapping:

1. **Database to Display Conversion**:
   ```python
   # In CustomTableView_v2.set_dataframe
   def set_dataframe(self, df: pd.DataFrame, custom_column_names: Optional[Dict[str, str]] = None):
       # Store original DataFrame with db_names for reference
       self._dataframe_db = df.copy()
       
       # Convert db_names to display_names for UI
       from fm.core.data_services.column_name_service import ColumnNameService
       display_df = ColumnNameService.apply_display_names(df, custom_column_names)
       
       # Set the display DataFrame to the table view
       self.table_view.set_dataframe(display_df)
   ```

2. **Column Width Management**:
   ```python
   # In TransactionViewPanel.set_transactions
   standard_widths = ColumnNameService.get_standard_column_widths()
   self.transaction_table.configure(column_widths=standard_widths)
   ```

3. **Module-Specific Defaults**:
   ```python
   # In TableConfig.set_default_visible_columns_for_module
   def set_default_visible_columns_for_module(self, module_name: str):
       self.default_visible_columns = ColumnNameService.get_default_visible_columns_for_module(module_name)
   ```

## User Preferences

The table view system stores and retrieves user preferences for column visibility:

1. **Saving Preferences** (in `TransactionViewPanel._save_column_selections`):
   - Visible columns are saved to module-specific config
   - Display names are converted back to database names for storage
   - Uses `ColumnNameService.get_reverse_mapping()` for conversion

2. **Loading Preferences**:
   - Default columns are loaded from config
   - The table view automatically applies these preferences

## Implications for Column System Unification

For the unified column system, we must ensure:

1. **Column Mapping Preservation**:
   - The new system must maintain the ability to map between database and display names
   - The `ColumnNameService` functionality must be preserved or enhanced in `ColumnManager`

2. **Module-Specific Defaults**:
   - Module-specific default visible columns must be preserved
   - The configuration mechanism must be maintained

3. **Column Width Information**:
   - Standard column width information must be preserved
   - The `get_standard_column_widths()` functionality must be maintained

4. **User Preference Handling**:
   - The mechanism for saving and loading user column preferences must be maintained
   - The config keys used for storing preferences should be preserved for backward compatibility

5. **Backward Compatibility**:
   - The table view system includes backward compatibility methods that must be preserved
   - Any changes to the column system must ensure these methods continue to work

## Migration Considerations

When migrating to the unified column system:

1. **ColumnNameService to ColumnManager**:
   - The `ColumnNameService` is marked as superseded by `ColumnManager`
   - The migration should ensure all functionality is properly transferred
   - The table view system should be updated to use `ColumnManager` instead

2. **StandardColumns Integration**:
   - The unified column system must maintain compatibility with the table view's expectations
   - Column metadata (display names, widths) must be preserved

3. **Configuration Integration**:
   - The table view's configuration system must work with the unified column system
   - Module-specific defaults must be maintained

4. **UI Component Compatibility**:
   - The table view components rely on the column system for display and configuration
   - Any changes must ensure the components can still properly display and configure columns

5. **DataFrame Conversion**:
   - The ability to convert between database and display names in DataFrames must be maintained
   - The `apply_display_names` and `apply_db_names` functionality must be preserved

## Specific Requirements

1. **Column Display Names**:
   - The table view expects display names to be human-readable (Title Case)
   - The unified system must maintain this convention

2. **Column Width Information**:
   - The table view uses character-based column widths
   - The unified system should maintain this approach

3. **Editable Column Specification**:
   - The table view allows specifying which columns are editable
   - The unified system should include this metadata

4. **Module-Specific Defaults**:
   - The table view supports module-specific default visible columns
   - The unified system should maintain this capability

5. **Backward Compatibility**:
   - The table view includes methods for backward compatibility
   - The unified system must ensure these continue to work
