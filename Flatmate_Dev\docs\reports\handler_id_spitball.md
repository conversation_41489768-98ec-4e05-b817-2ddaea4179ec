NOtes:
propositions to consider - can:

1. the match crtiteria can be gleaned from the existing attributes 

2. we give thre criteria a score 


acc pattern in metadata =20
acc pattern in data = 20
acc pattern in filename = 10
colnames match = 10
n_cols match = 5

minimum score of 15 required to pass 

We need to leverage _extract_account_number 
## Account Number Extraction with Scoring

### Current Implementation
The base `_extract_account_number` method already has the right logic flow:
1. Tries metadata first
2. Then data rows
3. Finally filename
4. Returns empty string if not found


## Enhanced Account Extraction with Source Tracking

### 1. Class-level Enum

To ensure the source is tracked using a robust, type-safe mechanism, we define an `Enum` at the class or module level.

```python
from enum import Enum

class AccountNumberSource(Enum):
    """Specifies the source from which an account number was extracted."""
    FROM_METADATA = "metadata"
    FROM_DATA = "data column"
    FROM_FILENAME = "filename"
```

### 2. Updated _extract_account_number Method

The method is updated to accept an optional `filepath` and return the `AccountNumberSource` enum member.

```python
def _extract_account_number(
    self, df: pd.DataFrame, filepath: str | None = None, return_source: bool = False
) -> str | tuple[str, AccountNumberSource | None]:
    """Extract account number using handler configuration.

    Args:
        df: DataFrame to search in.
        filepath: Optional path to the file, used for filename extraction.
        return_source: If True, returns a tuple of (account_number, source)
                     where source is an AccountNumberSource enum member or None.

    Returns:
        str: The extracted account number (if return_source=False).
        tuple: (account_number, source) if return_source=True.
    """
    attrs = self.account

    # Method 1: From metadata
    if attrs.in_metadata:
        try:
            row, col = attrs.location
            account_line = str(df.iloc[row, col])
            if attrs.pattern:
                match = re.search(attrs.pattern, account_line)
                if match:
                    account = match.group(1) if match.groups() else match.group(0)
                    return (account, AccountNumberSource.FROM_METADATA) if return_source else account
            else: # No pattern, return the whole cell content
                return (account_line.strip(), AccountNumberSource.FROM_METADATA) if return_source else account_line.strip()
        except (IndexError, AttributeError, KeyError) as e:
            log.warning(f"Could not find account number in metadata: {e}")

    # Method 2: From data column
    if attrs.in_data:
        try:
            col_name = attrs.location[1]
            if col_name in df.columns:
                for item in df[col_name].dropna():
                    item_str = str(item)
                    if attrs.pattern:
                        match = re.search(attrs.pattern, item_str)
                        if match:
                            account = match.group(1) if match.groups() else match.group(0)
                            return (account, AccountNumberSource.FROM_DATA) if return_source else account
        except (IndexError, KeyError) as e:
            log.warning(f"Could not find account number in data column: {e}")

    # Method 3: From filename
    if attrs.in_file_name and filepath:
        try:
            from pathlib import Path
            filename = Path(filepath).stem
            if attrs.pattern:
                match = re.search(attrs.pattern, filename)
                if match:
                    account = match.group(1) if match.groups() else match.group(0)
                    return (account, AccountNumberSource.FROM_FILENAME) if return_source else account
            else: # No pattern, return the whole filename
                return (filename, AccountNumberSource.FROM_FILENAME) if return_source else filename
        except Exception as e:
            log.warning(f"Could not extract account number from filename: {e}")

    return ("", None) if return_source else ""
```

### 3. Updated can_handle_file Method

This method now implements the full scoring logic.

```python
@classmethod
def can_handle_file(cls, filepath: str) -> bool:
    """Check if this handler can process the given file using a scoring system.

    Args:
        filepath: Path to the file to check.

    Returns:
        bool: True if this handler can process the file (score >= 15).
    """
    handler = cls()
    
    try:
        # Read the file preview
        rows_to_read = handler.columns.data_start_row + 2
        df = handler._read_csv(filepath, nrows=rows_to_read)

        if df is None or df.empty:
            log.error(f"Received empty DataFrame for {filepath}")
            return False

        # Get account number and source
        account_number, source = handler._extract_account_number(
            df, filepath=filepath, return_source=True
        )

        # Check column matches
        columns_match = False
        n_cols_match = len(df.columns) == handler.columns.n_source_cols
        
        if handler.columns.has_col_names and handler.columns.source_col_names:
            try:
                header_row = df.iloc[handler.columns.col_names_row].astype(str)
                expected_headers = set(handler.columns.source_col_names)
                actual_headers = set(header_row.str.strip().tolist())
                columns_match = expected_headers.issubset(actual_headers)
            except Exception as e:
                log.warning(f"Error checking columns for {filepath}: {e}")

        # Calculate score
        score = handler._calculate_can_handle_score(
            account_number=account_number,
            source=source,
            columns_match=columns_match,
            n_cols_match=n_cols_match
        )

        # Log the decision
        log.debug(
            f"Handler {cls.__name__} score for '{Path(filepath).name}': {score} "
            f"(Account: {account_number is not None}, Columns: {columns_match}, "
            f"NumCols: {n_cols_match})"
        )

        return score >= 15

    except Exception as e:
        log.warning(f"Error in {cls.__name__}.can_handle_file: {e}")
        return False

def _calculate_can_handle_score(
    self,
    account_number: Optional[str],
    source: Optional[AccountNumberSource],
    columns_match: bool,
    n_cols_match: bool
) -> int:
    """Calculate the handler match score.
    
    Scoring:
    - Account pattern in metadata/data: 20
    - Account pattern in filename: 10
    - Column names match: 10
    - Number of columns match: 5
    
    Minimum score to pass: 15
    """
    score = 0
    
    # Account number scoring
    if account_number:
        if source in [AccountNumberSource.FROM_METADATA, AccountNumberSource.FROM_DATA]:
            score += 20
        elif source == AccountNumberSource.FROM_FILENAME:
            score += 10
    
    # Column matching scoring
    if columns_match:
        score += 10
    if n_cols_match:
        score += 5
        
    return score

### Key Benefits

1.  **Single Source of Truth**
   - One method handles all account number extraction
   - No duplicate logic or code
   - Clear return type based on parameters

2.  **Flexible Usage**
   - Backward compatible with existing code
   - New code can access source information when needed
   - No changes to handler initialization

3.  **Clean Implementation**
   - Simple, focused method
   - Clear documentation
   - Maintains existing error handling

4.  **Efficient**
   - No wrapper methods or indirection
   - Early returns for better performance
   - Minimal memory usage

5.  **Backward Compatible**
   - Existing code continues to work
   - No changes needed for current implementations
   - Gradual migration to new features
