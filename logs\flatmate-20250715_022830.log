2025-07-15 02:28:30 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 02:28:31 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 02:28:31 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-07-15 02:28:31 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 02:28:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_load_from_db = True (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:28:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.remember_last_query = True (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:28:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.auto_load_on_startup = True (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:28:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_sort_column = date (Source: test_database_enhancement.py:test_database_enhancement)
2025-07-15 02:28:32 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.default_sort_order = descending (Source: test_database_enhancement.py:test_database_enhancement)
