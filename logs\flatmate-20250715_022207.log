2025-07-15 02:22:07 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 02:22:09 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 50 user preferences
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.columns.max_width = 40 (Source: test_config_helpers.py:test_config_helpers)
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.display.table_margin = 5 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.display.table_margin = 5
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.filters.default_days_back = 60 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:09 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.filters.default_days_back = 60
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Removed user preference categorize.display.table_margin from C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [INFO] - Reset config key to default: categorize.display.table_margin = None
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.display.row_height = 30 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.display.row_height = 30
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.columns.max_width = 50 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.columns.max_width = 50
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [DEBUG] - Removed user preference categorize.display.row_height from C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 02:22:10 - [fm.core.config.base_local_config_v2] [INFO] - Reset config key to default: categorize.display.row_height = None
