# Categorize Module Configuration
# Auto-generated from actual usage
# Organized by source file and function/class

# Source: Existing in core_config
#  - Function/Class: unknown_function
display:
  description_width: 40
  row_height: 25
  show_grid_lines: True
  table_margin: 2
filters:
  default_days: 30
  remember_last_filter: True


# Source: test_new_config.py
#  - Function/Class: __init__
display:
  header_height: 30

#  - Function/Class: configure_sorting
table:
  allow_multi_sort: True
  default_sort_column: 'date'
  sort_ascending: False

#  - Function/Class: initialize
display:
  show_filter_panel: True
filters:
  show_advanced: False

#  - Function/Class: initialize_keyboard_shortcuts
shortcuts:
  delete_key: 'Delete'
  filter_key: 'Ctrl+F'
  refresh_key: 'F5'
  save_key: 'Ctrl+S'

#  - Function/Class: setup_categorize_presenter
data:
  auto_refresh_seconds: 30
  page_size: 100
performance:
  lazy_load: True

#  - Function/Class: setup_columns
display:
  amount_width: 120
  category_width: 150
  date_width: 100

#  - Function/Class: setup_layout
display:
  border_width: 1
  scroll_bar_width: 15

