#!/usr/bin/env python3
"""
Test the DateFilterPane component.

This creates a standalone window to test the date filter component
with the application's dark theme styling.
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit
from PySide6.QtCore import Qt

from fm.gui.styles import load_styles
from fm.gui._shared_components.widgets.date_filter_pane import DateFilterPane


class DateFilterTestWindow(QMainWindow):
    """Test window for the DateFilterPane component."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Date Filter Pane Test")
        self.setGeometry(100, 100, 400, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("Date Filter Pane Component Test")
        title.setObjectName("heading")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create the date filter pane
        self.date_filter = DateFilterPane()
        layout.addWidget(self.date_filter)
        
        # Output area to show filter changes
        output_label = QLabel("Filter Output:")
        output_label.setStyleSheet("color: #CCCCCC; font-weight: bold; margin-top: 20px;")
        layout.addWidget(output_label)
        
        self.output_text = QTextEdit()
        self.output_text.setMaximumHeight(200)
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.output_text)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        # Connect signals
        self.date_filter.filter_changed.connect(self.on_filter_changed)
        self.date_filter.preset_selected.connect(self.on_preset_selected)
        
        # Initial output
        self.output_text.append("🧪 Date Filter Pane Test Started")
        self.output_text.append("=" * 40)
        
        # Show initial filter state
        initial_filter = self.date_filter.get_current_filter()
        self.output_text.append(f"Initial filter: {initial_filter}")
    
    def on_filter_changed(self, filter_data):
        """Handle filter changes."""
        self.output_text.append(f"\n🔄 Filter Changed:")
        self.output_text.append(f"  • Preset: {filter_data.get('preset', 'unknown')}")
        self.output_text.append(f"  • Start Date: {filter_data.get('start_date', 'None')}")
        self.output_text.append(f"  • End Date: {filter_data.get('end_date', 'None')}")
        
        # Calculate days if both dates present
        start = filter_data.get('start_date')
        end = filter_data.get('end_date')
        if start and end:
            days = (end - start).days
            self.output_text.append(f"  • Duration: {days} days")
        
        # Scroll to bottom
        self.output_text.verticalScrollBar().setValue(
            self.output_text.verticalScrollBar().maximum()
        )
    
    def on_preset_selected(self, preset_id):
        """Handle preset selection."""
        self.output_text.append(f"\n🎯 Preset Selected: {preset_id}")


def test_date_filter_pane():
    """Test the DateFilterPane component."""
    print("🧪 Testing DateFilterPane Component")
    print("=" * 40)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Load application styles
    try:
        styles = load_styles()
        app.setStyleSheet(styles)
        print("✅ Loaded application styles")
    except Exception as e:
        print(f"⚠️  Could not load styles: {e}")
        print("   Using default Qt styling")
    
    # Create and show test window
    window = DateFilterTestWindow()
    window.show()
    
    print("🎨 Test window created with DateFilterPane")
    print("📋 Features to test:")
    print("  • Click preset buttons (Week, Month, 3M, 6M, Year, All)")
    print("  • Toggle custom range (▼ Custom Range)")
    print("  • Change custom dates")
    print("  • Check filter output in text area")
    print("  • Verify dark theme styling")
    
    # Run the application
    return app.exec()


if __name__ == "__main__":
    sys.exit(test_date_filter_pane())
