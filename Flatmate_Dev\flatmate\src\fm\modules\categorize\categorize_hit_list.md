# categorise 
## currnent issues:
default should be to open from db  with last loaded db_query and and any filters appiled
default visible columns applied with default widths applied and details taking up any available space 
(logic should already be there somewhere)
default order should be applied this is an issue across the app
it used to be set by fm standard columns enum
the tableview columns drop down should use the same ordering logic 
The order should be set by how likely to be used 
The collumn display order should be remembered 
collumns for categorise df should be set either in groups or a method
should remember last selected columns for display which should over ride defaults 
columns should remember last set widths and order 
column autosizing seems disabled details coulumn should expand to take up available space up to a senisble max then notes if displayed 

This may go to the heart of the experimental local_confgig system which may not be be outputing a defaults yaml
- naming convention should this be called a settings.yaml or local_settings.yaml
and or should this be overidable by the local_config which inherits from _base_local config in core

Notes 