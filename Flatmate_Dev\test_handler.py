"""Simple test for statement handlers"""

from fm.modules.update_data.utils.statement_handlers._handler_registry import STATEMENT_HANDLERS

def test_handlers():
    """Test handler initialization"""
    print("Testing statement handlers initialization:")
    
    for handler_class in STATEMENT_HANDLERS:
        try:
            handler_name = handler_class.__name__
            print(f"- {handler_name}: ", end="")
            handler = handler_class()
            print("SUCCESS")
        except Exception as e:
            print(f"FAILED - {str(e)}")

if __name__ == "__main__":
    test_handlers()
