#!/usr/bin/env python3
"""Define comprehensive defaults for categorize module.

This script defines all the default values needed to address the issues
in the categorize hit list:
- Database loading with last query persistence
- Column ordering, visibility, and width management
- Default sort order
- Filter defaults
- Table view behavior
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

def define_comprehensive_defaults():
    """Define comprehensive defaults for all categorize module functionality."""
    print("🏗️  Defining Comprehensive Categorize Module Defaults")
    print("=" * 60)
    
    try:
        from fm.modules.categorize.config import config
        print("✅ Successfully imported categorize config")
        
        # Clear any existing config to start fresh
        initial_origins = config.get_key_origins()
        print(f"📊 Initial config keys: {len(initial_origins)}")
        
        print("\n🗄️  Database Loading & Query Persistence Defaults...")
        config.ensure_defaults({
            # Default behavior - open from database with last query
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.remember_last_filters': True,
            'categorize.database.default_days_back': 30,  # Default filter to 30 days
            'categorize.database.auto_load_on_startup': True,
            
            # Default sort order (addresses hit list issue)
            'categorize.database.default_sort_column': 'date',
            'categorize.database.default_sort_order': 'descending',  # Most recent first
            'categorize.database.remember_sort_preferences': True,
        })
        print("✅ Database defaults defined")
        
        print("\n📊 Column Management Defaults...")
        config.ensure_defaults({
            # Column ordering (addresses hit list issue)
            'categorize.columns.use_logical_ordering': True,
            'categorize.columns.order_by_usage_likelihood': True,
            'categorize.columns.remember_column_order': True,
            
            # Column visibility (addresses hit list issue)
            'categorize.columns.remember_visibility': True,
            'categorize.columns.remember_last_selected': True,
            'categorize.columns.override_defaults_with_last_used': True,
            
            # Column widths (addresses hit list issue)
            'categorize.columns.remember_widths': True,
            'categorize.columns.auto_size_enabled': True,
            'categorize.columns.max_column_width': 40,  # Character limit
            'categorize.columns.details_expand_to_fill': True,  # Key hit list requirement
            'categorize.columns.notes_expand_after_details': True,
        })
        print("✅ Column management defaults defined")
        
        print("\n🎯 Display & Layout Defaults...")
        config.ensure_defaults({
            # Default visible columns (logical order by usage likelihood)
            'categorize.display.default_visible_columns': [
                'date', 'details', 'amount', 'account', 'tags', 'category'
            ],
            
            # All available columns for selection
            'categorize.display.available_columns': [
                'date', 'details', 'amount', 'account', 'balance', 
                'tags', 'category', 'notes', 'source_uid', 'import_date'
            ],
            
            # Column widths (from StandardColumns)
            'categorize.display.column_widths': {
                'date': 12,
                'details': 40,  # Main content column
                'amount': 12,
                'account': 15,
                'balance': 12,
                'tags': 20,
                'category': 20,
                'notes': 30,
                'source_uid': 15,
                'import_date': 12
            },
            
            # Table appearance
            'categorize.display.table_margin': 2,
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.header_height': 30,
            'categorize.display.alternating_row_colors': True,
        })
        print("✅ Display defaults defined")
        
        print("\n🔍 Filter & Search Defaults...")
        config.ensure_defaults({
            # Filter panel
            'categorize.filters.panel_min_width': 200,
            'categorize.filters.panel_max_width': 300,
            'categorize.filters.default_days_back': 30,
            'categorize.filters.show_calendar_popup': True,
            'categorize.filters.remember_last_filter': True,
            'categorize.filters.show_advanced_filters': False,
            
            # Search behavior
            'categorize.search.case_sensitive': False,
            'categorize.search.search_all_columns': True,
            'categorize.search.highlight_matches': True,
        })
        print("✅ Filter defaults defined")
        
        print("\n📋 Table View Behavior Defaults...")
        config.ensure_defaults({
            # Table behavior
            'categorize.table.enable_sorting': True,
            'categorize.table.enable_filtering': True,
            'categorize.table.selection_mode': 'extended',  # Multi-select
            'categorize.table.enable_drag_drop': False,
            
            # Dropdown behavior (addresses hit list issue)
            'categorize.table.dropdown_use_logical_order': True,
            'categorize.table.dropdown_show_all_columns_first': True,
            'categorize.table.dropdown_add_divider_after_all': True,
            'categorize.table.dropdown_use_shortened_names': True,
            
            # Performance
            'categorize.table.lazy_loading': True,
            'categorize.table.page_size': 1000,
            'categorize.table.virtual_scrolling': True,
        })
        print("✅ Table behavior defaults defined")
        
        print("\n⌨️  User Experience Defaults...")
        config.ensure_defaults({
            # Keyboard shortcuts
            'categorize.shortcuts.save_key': 'Ctrl+S',
            'categorize.shortcuts.filter_key': 'Ctrl+F',
            'categorize.shortcuts.refresh_key': 'F5',
            'categorize.shortcuts.delete_key': 'Delete',
            'categorize.shortcuts.select_all_key': 'Ctrl+A',
            
            # Auto-save behavior
            'categorize.autosave.enabled': True,
            'categorize.autosave.interval_seconds': 30,
            'categorize.autosave.save_on_focus_loss': True,
            
            # Performance settings
            'categorize.performance.lazy_load': True,
            'categorize.performance.cache_results': True,
            'categorize.performance.debounce_search_ms': 300,
        })
        print("✅ User experience defaults defined")
        
        # Show what was created
        final_origins = config.get_key_origins()
        print(f"\n📊 Total config keys defined: {len(final_origins)}")
        
        # Group by namespace
        namespaces = {}
        for key, origin in final_origins.items():
            namespace = '.'.join(key.split('.')[:-1])
            if namespace not in namespaces:
                namespaces[namespace] = []
            namespaces[namespace].append(key)
        
        print(f"\n📋 Config Namespaces Created:")
        for namespace, keys in sorted(namespaces.items()):
            print(f"  🏷️  {namespace}: {len(keys)} keys")
        
        # Generate comprehensive defaults.yaml
        print(f"\n💾 Generating comprehensive defaults.yaml...")
        defaults_path = config.save_defaults_yaml()
        print(f"✅ Comprehensive defaults.yaml saved to: {defaults_path}")
        
        # Show preview
        yaml_content = config.generate_documented_yaml()
        lines = yaml_content.split('\n')
        print(f"\n📄 Comprehensive YAML Preview (first 25 lines):")
        for line in lines[:25]:
            print(f"  {line}")
        if len(lines) > 25:
            print(f"  ... and {len(lines) - 25} more lines")
        
        print(f"\n✅ Comprehensive defaults definition completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Comprehensive defaults definition failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = define_comprehensive_defaults()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Comprehensive defaults defined successfully!")
        print("📋 All categorize hit list issues should now have default values")
        print("🔧 Ready for implementation in Sprint 1.3")
    else:
        print("🔧 Issues found - need to investigate further")
    
    sys.exit(0 if success else 1)
