"""
Date Filter Pane Component

A compact, tasteful date filtering component with preset buttons and custom range.
Follows the application's dark theme design patterns with deep green accents.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QDateEdit, 
    QPushButton, QFrame, QSizePolicy
)


class DateFilterPane(QWidget):
    """
    A compact date filtering component with preset buttons and optional custom range.
    
    Features:
    - Quick preset buttons (Week, Month, 3M, 6M, Year, All)
    - Collapsible custom date range
    - Tasteful dark theme styling
    - Compact layout for space-constrained panels
    
    Signals:
        filter_changed: Emitted when date filter changes
        preset_selected: Emitted when a preset is selected
    """
    
    # Signals
    filter_changed = Signal(dict)  # {'start_date': date, 'end_date': date, 'preset': str}
    preset_selected = Signal(str)  # 'week', 'month', '3m', '6m', 'year', 'all'
    
    def __init__(self, parent=None):
        """Initialize the date filter pane."""
        super().__init__(parent)
        self.setObjectName("DateFilterPane")
        
        # State
        self._current_preset = 'all'
        self._custom_range_visible = False
        
        # Initialize UI
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
        
        # Set default to "All" (no filtering)
        self._select_preset('all')
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(6)
        
        # Header
        self.header_label = QLabel("Date Range")
        self.header_label.setObjectName("filter_header")
        main_layout.addWidget(self.header_label)
        
        # Preset buttons container
        self._create_preset_buttons(main_layout)
        
        # Custom range section (initially hidden)
        self._create_custom_range(main_layout)
        
        # Set size policy for compact layout
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)
    
    def _create_preset_buttons(self, parent_layout):
        """Create the preset button grid."""
        # Container frame for visual grouping
        preset_frame = QFrame()
        preset_frame.setObjectName("preset_frame")
        preset_layout = QVBoxLayout(preset_frame)
        preset_layout.setContentsMargins(4, 4, 4, 4)
        preset_layout.setSpacing(3)
        
        # First row: Week, Month, 3M
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(3)
        
        self.week_btn = self._create_preset_button("Week", "week")
        self.month_btn = self._create_preset_button("Month", "month")
        self.three_month_btn = self._create_preset_button("3M", "3m")
        
        row1_layout.addWidget(self.week_btn)
        row1_layout.addWidget(self.month_btn)
        row1_layout.addWidget(self.three_month_btn)
        
        # Second row: 6M, Year, All
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(3)
        
        self.six_month_btn = self._create_preset_button("6M", "6m")
        self.year_btn = self._create_preset_button("Year", "year")
        self.all_btn = self._create_preset_button("All", "all")
        
        row2_layout.addWidget(self.six_month_btn)
        row2_layout.addWidget(self.year_btn)
        row2_layout.addWidget(self.all_btn)
        
        preset_layout.addLayout(row1_layout)
        preset_layout.addLayout(row2_layout)
        
        # Custom range toggle
        self.custom_toggle_btn = QPushButton("▼ Custom Range")
        self.custom_toggle_btn.setObjectName("custom_toggle")
        self.custom_toggle_btn.setProperty("type", "filter_toggle")
        preset_layout.addWidget(self.custom_toggle_btn)
        
        parent_layout.addWidget(preset_frame)
        
        # Store buttons for easy access
        self._preset_buttons = {
            'week': self.week_btn,
            'month': self.month_btn,
            '3m': self.three_month_btn,
            '6m': self.six_month_btn,
            'year': self.year_btn,
            'all': self.all_btn
        }
    
    def _create_preset_button(self, text: str, preset_id: str) -> QPushButton:
        """Create a styled preset button."""
        btn = QPushButton(text)
        btn.setObjectName(f"preset_{preset_id}")
        btn.setProperty("type", "date_preset")
        btn.setProperty("preset_id", preset_id)
        btn.setCheckable(True)
        btn.setMaximumHeight(28)  # Compact height
        btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        return btn
    
    def _create_custom_range(self, parent_layout):
        """Create the custom date range section."""
        self.custom_frame = QFrame()
        self.custom_frame.setObjectName("custom_range_frame")
        self.custom_frame.setVisible(False)
        
        custom_layout = QVBoxLayout(self.custom_frame)
        custom_layout.setContentsMargins(4, 4, 4, 4)
        custom_layout.setSpacing(4)
        
        # From date
        from_layout = QHBoxLayout()
        from_layout.setSpacing(6)
        
        from_label = QLabel("From:")
        from_label.setObjectName("date_label")
        from_label.setMinimumWidth(35)
        
        self.from_date = QDateEdit()
        self.from_date.setObjectName("from_date")
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(datetime.now().date() - timedelta(days=30))
        self.from_date.setMaximumHeight(28)
        
        from_layout.addWidget(from_label)
        from_layout.addWidget(self.from_date)
        
        # To date
        to_layout = QHBoxLayout()
        to_layout.setSpacing(6)
        
        to_label = QLabel("To:")
        to_label.setObjectName("date_label")
        to_label.setMinimumWidth(35)
        
        self.to_date = QDateEdit()
        self.to_date.setObjectName("to_date")
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(datetime.now().date())
        self.to_date.setMaximumHeight(28)
        
        to_layout.addWidget(to_label)
        to_layout.addWidget(self.to_date)
        
        custom_layout.addLayout(from_layout)
        custom_layout.addLayout(to_layout)
        
        parent_layout.addWidget(self.custom_frame)
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Preset buttons
        for preset_id, btn in self._preset_buttons.items():
            btn.clicked.connect(lambda checked, pid=preset_id: self._select_preset(pid))
        
        # Custom range toggle
        self.custom_toggle_btn.clicked.connect(self._toggle_custom_range)
        
        # Date changes
        self.from_date.dateChanged.connect(self._on_custom_date_changed)
        self.to_date.dateChanged.connect(self._on_custom_date_changed)
    
    def _apply_styling(self):
        """Apply component-specific styling."""
        self.setStyleSheet("""
            /* Main container */
            QWidget#DateFilterPane {
                background-color: transparent;
            }
            
            /* Header label */
            QLabel#filter_header {
                color: #CCCCCC;
                font-size: 13px;
                font-weight: bold;
                margin-bottom: 2px;
            }
            
            /* Preset button frame */
            QFrame#preset_frame {
                background-color: #242424;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 2px;
            }
            
            /* Preset buttons */
            QPushButton[type="date_preset"] {
                background-color: #3B7443;
                color: #FFFFFF;
                border: none;
                border-radius: 3px;
                padding: 4px 6px;
                font-size: 12px;
                min-height: 20px;
            }
            
            QPushButton[type="date_preset"]:hover {
                background-color: #488E52;
            }
            
            QPushButton[type="date_preset"]:checked {
                background-color: #3B8A45;
                font-weight: bold;
            }
            
            /* Custom toggle button */
            QPushButton[type="filter_toggle"] {
                background-color: transparent;
                color: #B0B0B0;
                border: none;
                border-radius: 3px;
                padding: 4px;
                font-size: 11px;
                text-align: left;
            }
            
            QPushButton[type="filter_toggle"]:hover {
                color: #FFFFFF;
                background-color: #333333;
            }
            
            /* Custom range frame */
            QFrame#custom_range_frame {
                background-color: #202020;
                border: 1px solid #333333;
                border-radius: 3px;
                margin-top: 2px;
            }
            
            /* Date labels */
            QLabel#date_label {
                color: #CCCCCC;
                font-size: 11px;
            }
            
            /* Date edit widgets */
            QDateEdit#from_date, QDateEdit#to_date {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 11px;
            }
            
            QDateEdit#from_date:focus, QDateEdit#to_date:focus {
                border-color: #3B8A45;
            }
        """)
    
    def _select_preset(self, preset_id: str):
        """Select a preset and update the filter."""
        # Update button states
        for pid, btn in self._preset_buttons.items():
            btn.setChecked(pid == preset_id)
        
        self._current_preset = preset_id
        
        # Calculate date range
        end_date = datetime.now().date()
        start_date = None
        
        if preset_id == 'week':
            start_date = end_date - timedelta(days=7)
        elif preset_id == 'month':
            start_date = end_date - timedelta(days=30)
        elif preset_id == '3m':
            start_date = end_date - timedelta(days=90)
        elif preset_id == '6m':
            start_date = end_date - timedelta(days=180)
        elif preset_id == 'year':
            start_date = end_date - timedelta(days=365)
        elif preset_id == 'all':
            start_date = None
            end_date = None
        
        # Update custom date fields if we have dates
        if start_date and end_date:
            self.from_date.setDate(start_date)
            self.to_date.setDate(end_date)
        
        # Emit signals
        filter_data = {
            'start_date': start_date,
            'end_date': end_date,
            'preset': preset_id
        }
        
        self.preset_selected.emit(preset_id)
        self.filter_changed.emit(filter_data)
    
    def _toggle_custom_range(self):
        """Toggle the custom date range visibility."""
        self._custom_range_visible = not self._custom_range_visible
        self.custom_frame.setVisible(self._custom_range_visible)
        
        # Update button text
        arrow = "▲" if self._custom_range_visible else "▼"
        self.custom_toggle_btn.setText(f"{arrow} Custom Range")
    
    def _on_custom_date_changed(self):
        """Handle custom date range changes."""
        # Clear preset selection when custom dates are used
        for btn in self._preset_buttons.values():
            btn.setChecked(False)
        
        self._current_preset = 'custom'
        
        # Emit filter change
        filter_data = {
            'start_date': self.from_date.date().toPython(),
            'end_date': self.to_date.date().toPython(),
            'preset': 'custom'
        }
        
        self.filter_changed.emit(filter_data)
    
    # Public API
    def get_current_filter(self) -> Dict[str, Any]:
        """Get the current filter settings."""
        if self._current_preset == 'custom':
            return {
                'start_date': self.from_date.date().toPython(),
                'end_date': self.to_date.date().toPython(),
                'preset': 'custom'
            }
        elif self._current_preset == 'all':
            return {
                'start_date': None,
                'end_date': None,
                'preset': 'all'
            }
        else:
            # Recalculate preset dates
            end_date = datetime.now().date()
            days_map = {'week': 7, 'month': 30, '3m': 90, '6m': 180, 'year': 365}
            days = days_map.get(self._current_preset, 0)
            start_date = end_date - timedelta(days=days) if days > 0 else None
            
            return {
                'start_date': start_date,
                'end_date': end_date,
                'preset': self._current_preset
            }
    
    def set_preset(self, preset_id: str):
        """Set the current preset programmatically."""
        if preset_id in self._preset_buttons or preset_id == 'custom':
            self._select_preset(preset_id)
    
    def set_custom_range(self, start_date, end_date):
        """Set a custom date range."""
        self.from_date.setDate(start_date)
        self.to_date.setDate(end_date)
        self._on_custom_date_changed()
