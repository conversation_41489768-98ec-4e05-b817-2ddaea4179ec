"""
Date Filter Pane Component

A compact, tasteful date filtering component with preset buttons and custom range.
Follows the application's dark theme design patterns with deep green accents.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QDateEdit,
    QPushButton, QFrame, QSizePolicy, QComboBox
)


class DateFilterPane(QWidget):
    """
    A compact date filtering component with preset buttons and optional custom range.
    
    Features:
    - Quick preset buttons (Week, Month, 3M, 6M, Year, All)
    - Collapsible custom date range
    - Tasteful dark theme styling
    - Compact layout for space-constrained panels
    
    Signals:
        filter_changed: Emitted when date filter changes
        preset_selected: Emitted when a preset is selected
    """
    
    # Signals
    filter_changed = Signal(dict)  # {'start_date': date, 'end_date': date, 'preset': str}
    preset_selected = Signal(str)  # 'week', 'month', '3m', '6m', 'year', 'all'
    
    def __init__(self, parent=None):
        """Initialize the date filter pane."""
        super().__init__(parent)
        self.setObjectName("DateFilterPane")
        
        # State
        self._current_preset = 'all'
        self._custom_range_visible = False
        
        # Initialize UI
        self._init_ui()
        self._connect_signals()
        self._apply_styling()
        
        # Set default to "All" (no filtering)
        self._select_preset('all')
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(6)
        
        # Header
        self.header_label = QLabel("Date Range")
        self.header_label.setObjectName("filter_header")
        main_layout.addWidget(self.header_label)
        
        # Date range option menu
        self._create_range_menu(main_layout)
        
        # Custom range section (initially hidden)
        self._create_custom_range(main_layout)
        
        # Set size policy for compact layout
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)
    
    def _create_range_menu(self, parent_layout):
        """Create the date range option menu."""
        # Container frame for visual grouping
        range_frame = QFrame()
        range_frame.setObjectName("range_frame")
        range_layout = QVBoxLayout(range_frame)
        range_layout.setContentsMargins(6, 6, 6, 6)
        range_layout.setSpacing(6)

        # Date range selection
        range_selection_layout = QHBoxLayout()
        range_selection_layout.setSpacing(8)

        range_label = QLabel("Range:")
        range_label.setObjectName("range_label")
        range_label.setMinimumWidth(45)

        self.range_combo = QComboBox()
        self.range_combo.setObjectName("range_combo")

        # Add preset options
        self.range_combo.addItem("All Time", "all")
        self.range_combo.addItem("Last Week", "week")
        self.range_combo.addItem("Last Month", "month")
        self.range_combo.addItem("Last 3 Months", "3m")
        self.range_combo.addItem("Last 6 Months", "6m")
        self.range_combo.addItem("Last Year", "year")
        self.range_combo.addItem("Custom Range", "custom")

        # Set default to "All Time"
        self.range_combo.setCurrentIndex(0)

        range_selection_layout.addWidget(range_label)
        range_selection_layout.addWidget(self.range_combo)

        range_layout.addLayout(range_selection_layout)

        parent_layout.addWidget(range_frame)
    
    def _create_custom_range(self, parent_layout):
        """Create the custom date range section."""
        self.custom_frame = QFrame()
        self.custom_frame.setObjectName("custom_range_frame")
        self.custom_frame.setVisible(False)

        custom_layout = QVBoxLayout(self.custom_frame)
        custom_layout.setContentsMargins(6, 6, 6, 6)
        custom_layout.setSpacing(6)
        
        # From date
        from_layout = QHBoxLayout()
        from_layout.setSpacing(6)
        
        from_label = QLabel("From:")
        from_label.setObjectName("date_label")
        from_label.setMinimumWidth(35)
        
        self.from_date = QDateEdit()
        self.from_date.setObjectName("from_date")
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(datetime.now().date() - timedelta(days=30))
        self.from_date.setMaximumHeight(28)
        
        from_layout.addWidget(from_label)
        from_layout.addWidget(self.from_date)
        
        # To date
        to_layout = QHBoxLayout()
        to_layout.setSpacing(6)
        
        to_label = QLabel("To:")
        to_label.setObjectName("date_label")
        to_label.setMinimumWidth(35)
        
        self.to_date = QDateEdit()
        self.to_date.setObjectName("to_date")
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(datetime.now().date())
        self.to_date.setMaximumHeight(28)
        
        to_layout.addWidget(to_label)
        to_layout.addWidget(self.to_date)
        
        custom_layout.addLayout(from_layout)
        custom_layout.addLayout(to_layout)
        
        parent_layout.addWidget(self.custom_frame)
    
    def _connect_signals(self):
        """Connect widget signals."""
        # Range combo box
        self.range_combo.currentIndexChanged.connect(self._on_range_selection_changed)

        # Date changes
        self.from_date.dateChanged.connect(self._on_custom_date_changed)
        self.to_date.dateChanged.connect(self._on_custom_date_changed)
    
    def _apply_styling(self):
        """Apply component-specific styling."""
        self.setStyleSheet("""
            /* Main container */
            QWidget#DateFilterPane {
                background-color: transparent;
            }
            
            /* Header label */
            QLabel#filter_header {
                color: #CCCCCC;
                font-size: 13px;
                font-weight: bold;
                margin-bottom: 2px;
            }
            
            /* Range selection frame */
            QFrame#range_frame {
                background-color: #242424;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 2px;
            }

            /* Range label */
            QLabel#range_label {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: bold;
            }

            /* Range combo box */
            QComboBox#range_combo {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 12px;
                min-height: 20px;
            }

            QComboBox#range_combo:hover {
                border-color: #3B8A45;
            }

            QComboBox#range_combo:focus {
                border-color: #3B8A45;
            }

            QComboBox#range_combo::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox#range_combo::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #CCCCCC;
                margin-right: 4px;
            }
            
            /* Custom range frame */
            QFrame#custom_range_frame {
                background-color: #202020;
                border: 1px solid #333333;
                border-radius: 3px;
                margin-top: 2px;
            }
            
            /* Date labels */
            QLabel#date_label {
                color: #CCCCCC;
                font-size: 11px;
            }
            
            /* Date edit widgets */
            QDateEdit#from_date, QDateEdit#to_date {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 11px;
            }
            
            QDateEdit#from_date:focus, QDateEdit#to_date:focus {
                border-color: #3B8A45;
            }
        """)
    
    def _on_range_selection_changed(self, index):
        """Handle range selection changes."""
        preset_id = self.range_combo.itemData(index)
        if not preset_id:
            return

        self._current_preset = preset_id

        # Show/hide custom range based on selection
        if preset_id == 'custom':
            self.custom_frame.setVisible(True)
            # Don't calculate dates for custom - let user set them
            self._on_custom_date_changed()
            return
        else:
            self.custom_frame.setVisible(False)

        # Calculate date range for presets
        end_date = datetime.now().date()
        start_date = None

        if preset_id == 'week':
            start_date = end_date - timedelta(days=7)
        elif preset_id == 'month':
            start_date = end_date - timedelta(days=30)
        elif preset_id == '3m':
            start_date = end_date - timedelta(days=90)
        elif preset_id == '6m':
            start_date = end_date - timedelta(days=180)
        elif preset_id == 'year':
            start_date = end_date - timedelta(days=365)
        elif preset_id == 'all':
            start_date = None
            end_date = None

        # Update custom date fields if we have dates
        if start_date and end_date:
            self.from_date.setDate(start_date)
            self.to_date.setDate(end_date)

        # Emit signals
        filter_data = {
            'start_date': start_date,
            'end_date': end_date,
            'preset': preset_id
        }

        self.preset_selected.emit(preset_id)
        self.filter_changed.emit(filter_data)
    
    def _on_custom_date_changed(self):
        """Handle custom date range changes."""
        # Set combo to custom if not already
        if self._current_preset != 'custom':
            # Find and set custom option
            for i in range(self.range_combo.count()):
                if self.range_combo.itemData(i) == 'custom':
                    self.range_combo.setCurrentIndex(i)
                    break

        self._current_preset = 'custom'

        # Emit filter change
        filter_data = {
            'start_date': self.from_date.date().toPython(),
            'end_date': self.to_date.date().toPython(),
            'preset': 'custom'
        }

        self.filter_changed.emit(filter_data)
    
    # Public API
    def get_current_filter(self) -> Dict[str, Any]:
        """Get the current filter settings."""
        if self._current_preset == 'custom':
            return {
                'start_date': self.from_date.date().toPython(),
                'end_date': self.to_date.date().toPython(),
                'preset': 'custom'
            }
        elif self._current_preset == 'all':
            return {
                'start_date': None,
                'end_date': None,
                'preset': 'all'
            }
        else:
            # Recalculate preset dates
            end_date = datetime.now().date()
            days_map = {'week': 7, 'month': 30, '3m': 90, '6m': 180, 'year': 365}
            days = days_map.get(self._current_preset, 0)
            start_date = end_date - timedelta(days=days) if days > 0 else None
            
            return {
                'start_date': start_date,
                'end_date': end_date,
                'preset': self._current_preset
            }
    
    def set_preset(self, preset_id: str):
        """Set the current preset programmatically."""
        # Find the preset in the combo box and select it
        for i in range(self.range_combo.count()):
            if self.range_combo.itemData(i) == preset_id:
                self.range_combo.setCurrentIndex(i)
                break
    
    def set_custom_range(self, start_date, end_date):
        """Set a custom date range."""
        self.from_date.setDate(start_date)
        self.to_date.setDate(end_date)
        self._on_custom_date_changed()
