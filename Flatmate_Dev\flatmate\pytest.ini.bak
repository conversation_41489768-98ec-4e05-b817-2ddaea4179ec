[pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_functions = test_*
python_classes = Test*

# Default command line options (always used unless overridden)
addopts = 
    -v
    --cov=src
    --cov-report=term-missing
    --tb=short
    --showlocals
    -r a
    --durations=10
    -n auto
    --strict-markers

# Configure logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = src
branch = true
omit = 
    */tests/*
    */__pycache__/*
    */.*

[coverage:report]
show_missing = true
skip_covered = true

# Ignore these directories when discovering tests
norecursedirs = 
    .*
    build
    dist
    .venv_fm313
    venv
    .git
    __pycache__
    .pytest_cache
    .mypy_cache
    .vscode
    .idea

# Test markers (categorize your tests)
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: integration tests
    unit: unit tests
