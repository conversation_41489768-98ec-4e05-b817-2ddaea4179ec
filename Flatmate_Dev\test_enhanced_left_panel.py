#!/usr/bin/env python3
"""
Test the enhanced left panel with DateFilterPane integration.

This creates a standalone window to test the enhanced left panel
with the new date filter component.
"""

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget, QTextEdit, QVBoxLayout, QLabel
from PySide6.QtCore import Qt

from fm.gui.styles import load_styles
from fm.modules.categorize._view.components.left_panel.left_panel import LeftPanelWidget


class EnhancedLeftPanelTestWindow(QMainWindow):
    """Test window for the enhanced left panel."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced Left Panel Test - Categorize Module")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout (horizontal to show panel + output)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create the enhanced left panel
        self.left_panel = LeftPanelWidget()
        self.left_panel.setMaximumWidth(300)
        self.left_panel.setMinimumWidth(250)
        main_layout.addWidget(self.left_panel)
        
        # Create output area
        output_widget = QWidget()
        output_layout = QVBoxLayout(output_widget)
        
        # Title for output
        output_title = QLabel("Filter Events & Output")
        output_title.setStyleSheet("color: #CCCCCC; font-weight: bold; font-size: 14px;")
        output_layout.addWidget(output_title)
        
        # Output text area
        self.output_text = QTextEdit()
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        output_layout.addWidget(self.output_text)
        
        main_layout.addWidget(output_widget, 1)  # Give output area more space
        
        # Connect signals
        self.left_panel.load_db_requested.connect(self.on_load_db_requested)
        self.left_panel.files_select_requested.connect(self.on_files_select_requested)
        self.left_panel.cancel_clicked.connect(self.on_cancel_clicked)
        
        # Connect date filter signals if available
        if hasattr(self.left_panel, 'date_filter_pane'):
            self.left_panel.date_filter_pane.filter_changed.connect(self.on_date_filter_changed)
            self.left_panel.date_filter_pane.preset_selected.connect(self.on_preset_selected)
        
        # Initial output
        self.output_text.append("🧪 Restructured Left Panel Test Started")
        self.output_text.append("=" * 50)
        self.output_text.append("NEW LOGICAL HIERARCHY:")
        self.output_text.append("1. Load From - Choose data source first")
        self.output_text.append("2. Filter By - Pre-filter before loading (greyed until source set)")
        self.output_text.append("3. Load Data - Primary action button")
        self.output_text.append("4. Exit - Bottom")
        self.output_text.append("")
        self.output_text.append("Features to test:")
        self.output_text.append("• Source selection (database/file)")
        self.output_text.append("• Filter state changes based on source")
        self.output_text.append("• Date filter presets and custom range")
        self.output_text.append("• Account selection (populated for database)")
        self.output_text.append("• Load Data button (enabled when source selected)")
        self.output_text.append("• Clear All Filters")
        self.output_text.append("")
    

    
    def on_date_filter_changed(self, filter_data):
        """Handle date filter changes."""
        self.output_text.append(f"\n📅 Date Filter Changed:")
        self.output_text.append(f"  • Preset: {filter_data.get('preset', 'unknown')}")
        self.output_text.append(f"  • Start: {filter_data.get('start_date', 'None')}")
        self.output_text.append(f"  • End: {filter_data.get('end_date', 'None')}")
        
        # Calculate duration if both dates present
        start = filter_data.get('start_date')
        end = filter_data.get('end_date')
        if start and end:
            days = (end - start).days
            self.output_text.append(f"  • Duration: {days} days")
        
        self.scroll_to_bottom()
    
    def on_preset_selected(self, preset_id):
        """Handle preset selection."""
        self.output_text.append(f"\n🎯 Date Preset Selected: {preset_id}")
        self.scroll_to_bottom()
    
    def on_load_db_requested(self):
        """Handle load from database request."""
        self.output_text.append(f"\n🗄️  Load from Database Requested")
        
        # Get current filters to show what would be applied
        current_filters = self.left_panel.get_filters()
        if current_filters:
            self.output_text.append(f"  Would apply filters: {current_filters}")
        else:
            self.output_text.append(f"  Would load all data (no filters)")
        
        self.scroll_to_bottom()
    
    def on_files_select_requested(self):
        """Handle file selection request."""
        self.output_text.append(f"\n📁 File Selection Requested")
        self.scroll_to_bottom()
    
    def on_cancel_clicked(self):
        """Handle cancel/exit button."""
        self.output_text.append(f"\n❌ Cancel/Exit Clicked")
        self.scroll_to_bottom()
    
    def scroll_to_bottom(self):
        """Scroll output text to bottom."""
        scrollbar = self.output_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def test_enhanced_left_panel():
    """Test the enhanced left panel."""
    print("🧪 Testing Enhanced Left Panel with DateFilterPane")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Load application styles
    try:
        styles = load_styles()
        app.setStyleSheet(styles)
        print("✅ Loaded application styles")
    except Exception as e:
        print(f"⚠️  Could not load styles: {e}")
        print("   Using default Qt styling")
    
    # Create and show test window
    window = EnhancedLeftPanelTestWindow()
    window.show()
    
    print("🎨 Test window created with enhanced left panel")
    print("📋 Test the following features:")
    print("  • Date filter presets and custom range")
    print("  • Account selection dropdown")
    print("  • Apply Filters and Clear All buttons")
    print("  • Load from database/file options")
    print("  • Check output area for event logging")
    
    # Run the application
    return app.exec()


if __name__ == "__main__":
    sys.exit(test_enhanced_left_panel())
