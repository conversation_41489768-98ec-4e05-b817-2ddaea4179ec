"""
Core Database Module

This module provides the core database implementation including:
- Transaction repository pattern
- SQLite implementation
- Database models and types

For enhanced database services with column management, use fm.core.data_services
"""
from .sql_repository.transaction_repository import ImportResult, Transaction, ITransactionRepository
from .sql_repository.sqlite_repository import SQLiteTransactionRepository

__all__ = [
    'Transaction',
    'ImportResult',
    'TransactionRepository',
    'SQLiteTransactionRepository',
]
