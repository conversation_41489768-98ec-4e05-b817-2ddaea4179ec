# Qt Styling System Documentation

## Overview

FlatMate uses PySide6 (Qt for Python) with Qt Style Sheets (QSS) for application styling. QSS is similar to CSS but specifically designed for Qt widgets.

## Current Border Radius Standards

Based on analysis of `theme.qss`, FlatMate uses consistent border radius values:

### Border Radius Hierarchy
- **Primary/Secondary Buttons**: `border-radius: 6px` (larger interactive elements)
- **Containers (File tree, Toolbars)**: `border-radius: 4px` (panels and frames)
- **Small Inputs/Controls**: `border-radius: 3px` (text inputs, combo boxes, small buttons)

### Example Usage
```qss
/* Primary buttons - largest radius */
QPushButton[type="primary"] {
    border-radius: 6px;
}

/* Container elements - medium radius */
#file_tree, QFrame#TableViewToolbar {
    border-radius: 4px;
}

/* Small controls - smallest radius */
QLineEdit, QComboBox {
    border-radius: 3px;
}
```

## QSS Selector Patterns

### Object Name Selectors
Use `#objectName` for specific widget instances:
```qss
QFrame#TableViewToolbar {
    border: 1px solid #2A5A3A;
    border-radius: 4px;
}
```

### Widget Type Selectors
Use `WidgetType` for all widgets of that type:
```qss
QPushButton {
    background-color: var(--color-bg-dark);
}
```

### Combined Selectors
Use `ParentType#objectName ChildType` for specific hierarchies:
```qss
QFrame#TableViewToolbar QPushButton {
    border-radius: 3px;
}
```

## Variable System

FlatMate uses CSS custom properties (variables) defined in `palette.qss`:

### Color Variables
```qss
:root {
    --color-primary: #3B8A45;        /* Deep green */
    --color-secondary: #3B7443;      /* Secondary green */
    --color-bg-dark: #1E1E1E;        /* Dark background */
    --color-border: #333333;         /* Border color */
    --color-text-primary: #FFFFFF;   /* White text */
    --color-text-secondary: #CCCCCC; /* Gray text */
}
```

### Using Variables
```qss
QFrame#TableViewToolbar {
    border: 1px solid var(--color-primary);
    background-color: var(--color-panel-bg);
}
```

## Common QSS Properties

### Border Properties
```qss
border: 1px solid #color;           /* Width, style, color */
border-radius: 4px;                 /* Rounded corners */
border-top: 2px solid #color;       /* Individual sides */
```

### Background Properties
```qss
background-color: #color;           /* Solid color */
background: transparent;            /* Transparent */
background: qlineargradient(...);   /* Gradient */
```

### Text Properties
```qss
color: #color;                      /* Text color */
font-size: 14px;                    /* Font size */
font-weight: bold;                  /* Font weight */
```

### Layout Properties
```qss
padding: 4px;                       /* All sides */
padding: 4px 8px;                   /* Vertical, horizontal */
margin: 2px;                        /* External spacing */
```

## Widget-Specific Styling

### QPushButton States
```qss
QPushButton {
    /* Normal state */
}
QPushButton:hover {
    /* Mouse over */
}
QPushButton:pressed {
    /* Being clicked */
}
QPushButton:disabled {
    /* Disabled state */
}
```

### QComboBox Parts
```qss
QComboBox {
    /* Main combo box */
}
QComboBox::drop-down {
    /* Dropdown arrow area */
}
QComboBox::down-arrow {
    /* The arrow itself */
}
```

## Best Practices

### 1. Use Consistent Naming
- Set `objectName` with `setObjectName("WidgetName")`
- Use descriptive names: `TableViewToolbar`, `FilterGroup`

### 2. Avoid Hardcoded Colors
```qss
/* Bad */
border: 1px solid #3B8A45;

/* Good */
border: 1px solid var(--color-primary);
```

### 3. Test Incrementally
- Add one style property at a time
- Comment out styles for debugging
- Use specific selectors to avoid conflicts

### 4. Handle All States
```qss
QPushButton {
    background-color: var(--color-bg-dark);
    color: var(--color-text-primary);
}
QPushButton:hover {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);  /* Explicit color */
}
```

## Known Issues & Limitations

### 1. No Constants for Dimensions
Qt doesn't support variables for dimensions like `border-radius`. Must use hardcoded values consistently. #? ( we think ) the var pattern above w orks for colours?

### 2. Inheritance Quirks
QSS inheritance differs from CSS. Be explicit with properties to avoid unexpected behavior.

### 3. Selector Specificity
More specific selectors override less specific ones:
```qss
/* Less specific */
QPushButton { color: red; }

/* More specific - wins */
QFrame#Toolbar QPushButton { color: blue; }
```

## Debugging QSS

### 1. Comment Out Styles
```qss
/* Temporarily disable for testing */
/*
QFrame#TableViewToolbar {
    border: 1px solid var(--color-primary);
}
*/
```

### 2. Use Simple Test Styles
```qss
/* Test with obvious colors */
QFrame#TableViewToolbar {
    background-color: red;  /* Should be obvious if applied */
}
```

### 3. Check Object Names
Ensure `setObjectName()` is called in Python:
```python
self.setObjectName("TableViewToolbar")
```

## References

- [Qt Style Sheets Reference](https://doc.qt.io/qt-6/stylesheet-reference.html)
- [PySide6 Documentation](https://doc.qt.io/qtforpython/)
- [Qt Style Sheets Examples](https://doc.qt.io/qt-6/stylesheet-examples.html)
