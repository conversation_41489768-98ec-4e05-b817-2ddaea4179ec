#!/usr/bin/env python3
"""
Test script to check what columns are being created.
"""

import sys
sys.path.insert(0, 'flatmate/src')

from fm.core.data_services.standards.columns import Columns

def test_columns():
    """Test what columns are available."""
    
    print("=== ALL COLUMNS ===")
    all_cols = Columns.get_all_columns()
    for col in all_cols:
        print(f"{col.db_name:20} | {col.display_name:20} | {col.groups}")
    
    print(f"\nTotal columns: {len(all_cols)}")
    
    print("\n=== TRANSACTION COLUMNS (excludes db_system) ===")
    trans_cols = Columns.get_transaction_columns()
    for col in trans_cols:
        print(f"{col.db_name:20} | {col.display_name:20} | {col.groups}")
    
    print(f"\nTransaction columns: {len(trans_cols)}")
    
    print("\n=== KEY COLUMNS CHECK ===")
    print(f"SOURCE_UID: {Columns.SOURCE_UID.db_name} - {Columns.SOURCE_UID.groups}")
    print(f"DB_UID: {Columns.DB_UID.db_name} - {Columns.DB_UID.groups}")
    print(f"UNIQUE_ID: {Columns.UNIQUE_ID.db_name} - {Columns.UNIQUE_ID.groups}")
    
    # Check if our key columns are in the lists
    all_db_names = {col.db_name for col in all_cols}
    trans_db_names = {col.db_name for col in trans_cols}
    
    print(f"\nSOURCE_UID in all_columns: {'source_uid' in all_db_names}")
    print(f"DB_UID in all_columns: {'db_uid' in all_db_names}")
    print(f"SOURCE_UID in transaction_columns: {'source_uid' in trans_db_names}")
    print(f"DB_UID in transaction_columns: {'db_uid' in trans_db_names}")

if __name__ == "__main__":
    test_columns()
