# Data Pipeline Analysis: Suspicious Transactions and Data Consistency

**Date:** 2025-07-14  
**Author:** Cascade  
**Status:** Investigation in Progress

## 1. Executive Summary

During routine data import operations, the system flagged several transactions as suspicious due to having non-zero amounts but no corresponding balance change. This report documents the investigation into these anomalies and provides recommendations for resolution.

## 2. Incident Details

### 2.1 Affected Transactions

| Date       | Account           | Amount  | Balance | Details                              |
|------------|-------------------|---------|---------|--------------------------------------|
| 2023-12-10 | 38-9004-0646977-04 | -$7.39  | $45.75  | POS W/D FOUR SQUARE H-10:22          |
| 2024-09-27 | 38-9004-0646977-00 | -$1.00  | $0.07   | TRANSFER TO Q M SHAW-WILLIAMS - 04   |
| 2025-04-03 | 38-9004-0646977-00 | -$500.00| $500.02 | TRANSFER TO Q M SHAW-WILLIAMS - 01   |
| 2025-04-15 | 38-9004-0646977-00 | -$20.00 | $326.02 | TRANSFER TO Q M SHAW-WILLIAMS - 04   |

### 2.2 Key Observations

1. **Pattern Recognition**:
   - 3/4 flagged transactions are internal transfers to the same recipient
   - All transactions are debits (negative amounts)
   - Affects two different accounts

2. **Data Discrepancies**:
   - Transaction from 2023-12-10 exists in database but not in master CSV
   - Balance remains constant despite transaction amounts

## 3. Root Cause Analysis

### 3.1 Technical Implementation

#### Current Detection Logic
```python
def flag_suspicious_transactions(df):
    # Current implementation flags transactions where:
    # 1. Amount is not zero
    # 2. Balance remains unchanged from previous transaction
    # 3. Not the first transaction in account history
```

#### Data Flow
1. Source Files → Statement Handlers → DataFrame Processing → Database
2. Potential gap in tracking transaction lineage through this pipeline

### 3.2 Identified Issues

1. **False Positives in Detection**
   - Internal transfers between accounts may not affect overall balance
   - Current logic doesn't account for this scenario

2. **Data Consistency Gaps**
   - No clear audit trail from source file to database
   - Difficult to trace origin of transactions

3. **Date Handling**
   - Potential for inconsistent date parsing (DD/MM vs MM/DD)
   - Could lead to misaligned transaction ordering

## 4. Recommended Actions

### 4.1 Immediate Fixes

1. **Update Suspicious Transaction Logic**
   ```python
   def _is_internal_transfer(details: str) -> bool:
       """Identify internal transfers that shouldn't affect balance."""
       return any(x in details.upper() for x in 
                 ['TRANSFER TO', 'TRANSFER FROM', 'INTERNAL TRANSFER'])
   
   def flag_suspicious_transactions(df):
       # Skip internal transfers from balance validation
       df['is_internal'] = df['details'].apply(_is_internal_transfer)
       # Rest of the logic...
   ```

2. **Implement Data Validation**
   - Add checksum validation for imported files
   - Verify transaction integrity before database updates
   - Log all data modifications with timestamps

### 4.2 Long-term Improvements

1. **Enhanced Data Lineage**
   ```python
   class TransactionLineage:
       def __init__(self):
           self.source_file: str
           self.import_timestamp: datetime
           self.raw_data: dict
           self.transformations: List[dict]
   ```

2. **Comprehensive Logging**
   - Log file hashes
   - Record all data transformations
   - Track user actions and system events

3. **Automated Reconciliation**
   - Daily validation of database against source files
   - Alerting for data inconsistencies
   - Self-healing mechanisms for common issues

## 5. Implementation Plan

### Phase 1: Critical Fixes (1-2 days)
- [ ] Update transaction validation logic
- [ ] Add transaction source tracking
- [ ] Implement basic reconciliation checks

### Phase 2: Enhanced Monitoring (3-5 days)
- [ ] Set up detailed logging
- [ ] Create reconciliation reports
- [ ] Implement alerting system

### Phase 3: Preventative Measures (1 week+)
- [ ] Refactor data pipeline for better traceability
- [ ] Add comprehensive test coverage
- [ ] Document all data flows and validations

## 6. Conclusion

The identified issues primarily stem from overzealous transaction validation and insufficient data lineage tracking. The recommended changes will improve data integrity while reducing false positives in the suspicious transaction detection system.

**Next Steps:**
1. Review and implement Phase 1 fixes
2. Schedule a follow-up review after implementation
3. Monitor system behavior post-deployment
4. Update documentation with data flow diagrams

---
*Last Updated: 2025-07-14 18:26 NZST*
